Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.HAL_GPIO_EXTI_Callback) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.HAL_GPIO_EXTI_Callback) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    main.o(i.HAL_GPIO_EXTI_Callback) refers to flash.o(i.Flash_Write) for Flash_Write
    main.o(i.HAL_GPIO_EXTI_Callback) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.HAL_GPIO_EXTI_Callback) refers to main.o(.data) for a
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue) for HAL_TIM_ReadCapturedValue
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to main.o(.data) for TIM2CH1_CAP_STA
    main.o(i.HAL_TIM_IC_CaptureCallback) refers to tim.o(.bss) for htim2
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to main.o(i.SEG_Refresh) for SEG_Refresh
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim6
    main.o(i.HAL_UARTEx_RxEventCallback) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    main.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart5
    main.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.data) for usart1_data_len
    main.o(i.HAL_UARTEx_RxEventCallback) refers to main.o(.bss) for usart5_rx_buf
    main.o(i.HAL_UART_RxCpltCallback) refers to exit_usart.o(i.K210) for K210
    main.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.HAL_UART_RxCpltCallback) refers to exit_usart.o(i.TFLUNA) for TFLUNA
    main.o(i.HAL_UART_RxCpltCallback) refers to exit_usart.o(i.MKF_K210) for MKF_K210
    main.o(i.HAL_UART_RxCpltCallback) refers to main.o(.data) for usart2_rx
    main.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart2
    main.o(i.SEG_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SEG_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(i.SEG_GPIO_Init) refers to main.o(.data) for SEG_Pins
    main.o(i.SEG_Refresh) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.SEG_Refresh) refers to main.o(.data) for Display_Buffer
    main.o(i.SEG_SetChar) refers to main.o(.constdata) for SEG_Table
    main.o(i.SEG_SetChar) refers to main.o(.data) for Display_Buffer
    main.o(i.SEG_ShowNumber) refers to main.o(i.SEG_SetChar) for SEG_SetChar
    main.o(i.SHURUBUHUO) refers to main.o(.data) for TIM2CH1_CAP_STA
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to adc.o(i.MX_ADC2_Init) for MX_ADC2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM8_Init) for MX_TIM8_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to main.o(i.SEG_GPIO_Init) for SEG_GPIO_Init
    main.o(i.main) refers to eeprom_emul.o(i.EE_Init) for EE_Init
    main.o(i.main) refers to flash.o(i.Flash_Init) for Flash_Init
    main.o(i.main) refers to eeprom_emul.o(i.EE_Read) for EE_Read
    main.o(i.main) refers to eeprom_emul.o(i.EE_Write) for EE_Write
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to lcd_spi_130.o(i.SPI_LCD_Init) for SPI_LCD_Init
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(i.main) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.main) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    main.o(i.main) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    main.o(i.main) refers to lcd_spi_130.o(i.ips200_showimage) for ips200_showimage
    main.o(i.main) refers to flash.o(i.Flash_Read) for Flash_Read
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_GetState) for HAL_ADC_GetState
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to main.o(i.SEG_ShowNumber) for SEG_ShowNumber
    main.o(i.main) refers to main.o(.data) for boot
    main.o(i.main) refers to adc.o(.bss) for hadc1
    main.o(i.main) refers to tim.o(.bss) for htim3
    main.o(i.main) refers to main.o(.constdata) for gImage_pic
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for hadc1
    adc.o(i.MX_ADC2_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC2_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC2_Init) refers to adc.o(.bss) for hadc2
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for hi2c1
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    spi.o(i.HAL_SPI_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for hspi1
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    tim.o(i.MX_TIM4_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for htim4
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for htim6
    tim.o(i.MX_TIM8_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM8_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM8_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM8_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM8_Init) refers to tim.o(.bss) for htim8
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for huart4
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for huart5
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for huart2
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for huart3
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for huart6
    usart.o(i.fgetc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    usart.o(i.fgetc) refers to usart.o(.bss) for huart5
    usart.o(i.fputc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.EXTI15_10_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for htim6
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_write) for atk_ms901m_uart_rx_fifo_write
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to main.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAError) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to main.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to main.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.get_accel_prod_shift) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.get_st_biases) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.get_st_biases) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.get_st_biases) refers to ldiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.gyro_self_test) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_reg) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_reg) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_int_status) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_temperature) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_init) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_6050_accel_bias) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_6050_accel_bias) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_6500_accel_bias) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_6500_accel_bias) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_6500_gyro_bias) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_6500_gyro_bias) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo_stream) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_mem) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_read_mem) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_reg) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reg_dump) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_reg_dump) refers to printfa.o(i.__0printf) for __2printf
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reset_fifo) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_reset_fifo) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_bias_6050_reg) refers to inv_mpu.o(i.mpu_read_6050_accel_bias) for mpu_read_6050_accel_bias
    inv_mpu.o(i.mpu_set_accel_bias_6050_reg) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_accel_bias_6050_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_bias_6500_reg) refers to inv_mpu.o(i.mpu_read_6500_accel_bias) for mpu_read_6500_accel_bias
    inv_mpu.o(i.mpu_set_accel_bias_6500_reg) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_accel_bias_6500_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_fsr) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_bypass) refers to my_mpu6050.o(i.i2c2_read) for i2c2_read
    inv_mpu.o(i.mpu_set_bypass) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_bypass) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_gyro_bias_reg) refers to inv_mpu.o(i.mpu_read_6500_gyro_bias) for mpu_read_6500_gyro_bias
    inv_mpu.o(i.mpu_set_gyro_bias_reg) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_gyro_bias_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_latched) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_lpf) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sensors) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_write_mem) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.set_int_enable) refers to my_mpu6050.o(i.i2c2_write) for i2c2_write
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for st
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu_dmp_motion_driver.o(i.decode_gesture) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to memseta.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to memseta.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for dmp_memory
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(i.decode_gesture) for decode_gesture
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    my_mpu6050.o(i.MPU6050_Read_Temp) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    my_mpu6050.o(i.MPU6050_Read_Temp) refers to my_mpu6050.o(.data) for Rec_Data
    my_mpu6050.o(i.MPU6050_Read_Temp) refers to i2c.o(.bss) for hi2c1
    my_mpu6050.o(i.MPU6050_Read_Temp) refers to my_mpu6050.o(.bss) for My_Mpu6050
    my_mpu6050.o(i.dmp_getdata) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    my_mpu6050.o(i.dmp_getdata) refers to f2d.o(.text) for __aeabi_f2d
    my_mpu6050.o(i.dmp_getdata) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    my_mpu6050.o(i.dmp_getdata) refers to dmul.o(.text) for __aeabi_dmul
    my_mpu6050.o(i.dmp_getdata) refers to d2f.o(.text) for __aeabi_d2f
    my_mpu6050.o(i.dmp_getdata) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    my_mpu6050.o(i.dmp_getdata) refers to my_mpu6050.o(.bss) for hal
    my_mpu6050.o(i.dmp_getdata) refers to my_mpu6050.o(.data) for more
    my_mpu6050.o(i.gyro_data_ready_cb) refers to my_mpu6050.o(.bss) for hal
    my_mpu6050.o(i.i2c2_read) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    my_mpu6050.o(i.i2c2_read) refers to i2c.o(.bss) for hi2c1
    my_mpu6050.o(i.i2c2_write) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    my_mpu6050.o(i.i2c2_write) refers to i2c.o(.bss) for hi2c1
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    my_mpu6050.o(i.mpu_dmp_init) refers to my_mpu6050.o(i.SYSTEM_Reset) for SYSTEM_Reset
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    my_mpu6050.o(i.mpu_dmp_init) refers to memseta.o(.text) for __aeabi_memclr
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    my_mpu6050.o(i.mpu_dmp_init) refers to my_mpu6050.o(i.inv_row_2_scale) for inv_row_2_scale
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    my_mpu6050.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    my_mpu6050.o(i.mpu_dmp_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_mpu6050.o(i.mpu_dmp_init) refers to my_mpu6050.o(.bss) for hal
    my_mpu6050.o(i.mpu_dmp_init) refers to my_mpu6050.o(.data) for gyro_orientation
    nrf24l01.o(i.NRF24L01_Check) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.NRF24L01_Check) refers to nrf24l01.o(i.NRF24L01_Read_Buf) for NRF24L01_Read_Buf
    nrf24l01.o(i.NRF24L01_Check) refers to printfa.o(i.__0printf) for __2printf
    nrf24l01.o(i.NRF24L01_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Init) refers to nrf24l01.o(i.NRF_delay_us) for NRF_delay_us
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to nrf24l01.o(i.nRF24_SPI_Send_Byte) for nRF24_SPI_Send_Byte
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Receive) for HAL_SPI_Receive
    nrf24l01.o(i.NRF24L01_Read_Buf) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF24L01_Read_Reg) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Read_Reg) refers to nrf24l01.o(i.nRF24_SPI_Send_Byte) for nRF24_SPI_Send_Byte
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.NRF24L01_Read_Reg) for NRF24L01_Read_Reg
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.NRF24L01_RxPacket) refers to printfa.o(i.__0printf) for __2printf
    nrf24l01.o(i.NRF24L01_RxPacket) refers to nrf24l01.o(i.NRF24L01_Read_Buf) for NRF24L01_Read_Buf
    nrf24l01.o(i.NRF24L01_TxPacket) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.NRF24L01_TxPacket) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.NRF24L01_Read_Reg) for NRF24L01_Read_Reg
    nrf24l01.o(i.NRF24L01_TxPacket) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.NRF24L01_TxPacket) refers to printfa.o(i.__0printf) for __2printf
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to nrf24l01.o(i.nRF24_SPI_Send_Byte) for nRF24_SPI_Send_Byte
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    nrf24l01.o(i.NRF24L01_Write_Buf) refers to spi.o(.bss) for hspi1
    nrf24l01.o(i.NRF24L01_Write_Reg) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.NRF24L01_Write_Reg) refers to nrf24l01.o(i.nRF24_SPI_Send_Byte) for nRF24_SPI_Send_Byte
    nrf24l01.o(i.RX_Mode) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.RX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.RX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.RX_Mode) refers to nrf24l01.o(.constdata) for INIT_ADDR0
    nrf24l01.o(i.TX_Mode) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    nrf24l01.o(i.TX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Buf) for NRF24L01_Write_Buf
    nrf24l01.o(i.TX_Mode) refers to nrf24l01.o(i.NRF24L01_Write_Reg) for NRF24L01_Write_Reg
    nrf24l01.o(i.TX_Mode) refers to nrf24l01.o(.constdata) for INIT_ADDR0
    nrf24l01.o(i.nRF24_SPI_Send_Byte) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    nrf24l01.o(i.nRF24_SPI_Send_Byte) refers to spi.o(.bss) for hspi1
    key.o(i.Key_Scam) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.Key_Scam) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    key.o(i.Key_Scam) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    key.o(i.Key_Scam) refers to printfa.o(i.__0printf) for __2printf
    motor.o(i.BACK) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.BACK) refers to motor.o(.data) for Target_L
    motor.o(i.FORWARD) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.FORWARD) refers to motor.o(.data) for Target_L
    motor.o(i.LEFT) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.RIGHT) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.STOP) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i.STOP) refers to motor.o(.data) for Target_L
    motor.o(i.target) refers to motor.o(.data) for Target_L
    oled.o(i.I2C_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_Refresh) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_Refresh) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_0806
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(.constdata) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.Send_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    exit_usart.o(i.K210) refers to printfa.o(i.__0printf) for __2printf
    exit_usart.o(i.K210) refers to exit_usart.o(.bss) for USART1_RX_BUF
    exit_usart.o(i.K210) refers to exit_usart.o(.data) for i3
    exit_usart.o(i.MKF_K210) refers to printfa.o(i.__0printf) for __2printf
    exit_usart.o(i.MKF_K210) refers to exit_usart.o(.bss) for USART1_RX_BUF
    exit_usart.o(i.MKF_K210) refers to exit_usart.o(.data) for i3
    exit_usart.o(i.TFLUNA) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    exit_usart.o(i.TFLUNA) refers to printfa.o(i.__0printf) for __2printf
    exit_usart.o(i.TFLUNA) refers to exit_usart.o(.bss) for uart
    exit_usart.o(i.TFLUNA) refers to usart.o(.bss) for huart4
    exit_usart.o(i.TFLUNA) refers to exit_usart.o(.data) for check
    lcd_fonts.o(.data) refers to lcd_fonts.o(.constdata) for Chinese_1212
    lcd_spi_130.o(i.HAL_SPI3_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    lcd_spi_130.o(i.HAL_SPI3_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd_spi_130.o(i.HAL_SPI3_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_spi_130.o(i.LCD_Clear) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_Clear) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_ClearRect) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_ClearRect) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.LCD_DisplayChar) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_DisplayChar) refers to lcd_spi_130.o(i.LCD_WriteBuff) for LCD_WriteBuff
    lcd_spi_130.o(i.LCD_DisplayChar) refers to lcd_spi_130.o(.data) for LCD_AsciiFonts
    lcd_spi_130.o(i.LCD_DisplayChar) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DisplayChinese) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_DisplayChinese) refers to lcd_spi_130.o(i.LCD_WriteBuff) for LCD_WriteBuff
    lcd_spi_130.o(i.LCD_DisplayChinese) refers to lcd_spi_130.o(.data) for LCD_CHFonts
    lcd_spi_130.o(i.LCD_DisplayChinese) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DisplayDecimals) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd_spi_130.o(i.LCD_DisplayDecimals) refers to lcd_spi_130.o(i.LCD_DisplayString) for LCD_DisplayString
    lcd_spi_130.o(i.LCD_DisplayDecimals) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DisplayNumber) refers to printfa.o(i.__0sprintf) for __2sprintf
    lcd_spi_130.o(i.LCD_DisplayNumber) refers to lcd_spi_130.o(i.LCD_DisplayString) for LCD_DisplayString
    lcd_spi_130.o(i.LCD_DisplayNumber) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DisplayString) refers to lcd_spi_130.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lcd_spi_130.o(i.LCD_DisplayString) refers to lcd_spi_130.o(.data) for LCD_AsciiFonts
    lcd_spi_130.o(i.LCD_DisplayString) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DisplayText) refers to lcd_spi_130.o(i.LCD_DisplayChar) for LCD_DisplayChar
    lcd_spi_130.o(i.LCD_DisplayText) refers to lcd_spi_130.o(i.LCD_DisplayChinese) for LCD_DisplayChinese
    lcd_spi_130.o(i.LCD_DisplayText) refers to lcd_spi_130.o(.data) for LCD_AsciiFonts
    lcd_spi_130.o(i.LCD_DrawCircle) refers to lcd_spi_130.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd_spi_130.o(i.LCD_DrawCircle) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawEllipse) refers to lcd_spi_130.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd_spi_130.o(i.LCD_DrawEllipse) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawImage) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_DrawImage) refers to lcd_spi_130.o(i.LCD_WriteBuff) for LCD_WriteBuff
    lcd_spi_130.o(i.LCD_DrawImage) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawLine) refers to lcd_spi_130.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd_spi_130.o(i.LCD_DrawLine) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawLine_H) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_DrawLine_H) refers to lcd_spi_130.o(i.LCD_WriteBuff) for LCD_WriteBuff
    lcd_spi_130.o(i.LCD_DrawLine_H) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawLine_V) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_DrawLine_V) refers to lcd_spi_130.o(i.LCD_WriteBuff) for LCD_WriteBuff
    lcd_spi_130.o(i.LCD_DrawLine_V) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawPoint) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_DrawPoint) refers to lcd_spi_130.o(i.LCD_WriteData_16bit) for LCD_WriteData_16bit
    lcd_spi_130.o(i.LCD_DrawPoint) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_DrawRect) refers to lcd_spi_130.o(i.LCD_DrawLine_H) for LCD_DrawLine_H
    lcd_spi_130.o(i.LCD_DrawRect) refers to lcd_spi_130.o(i.LCD_DrawLine_V) for LCD_DrawLine_V
    lcd_spi_130.o(i.LCD_FillCircle) refers to lcd_spi_130.o(i.LCD_DrawLine_V) for LCD_DrawLine_V
    lcd_spi_130.o(i.LCD_FillCircle) refers to lcd_spi_130.o(i.LCD_DrawCircle) for LCD_DrawCircle
    lcd_spi_130.o(i.LCD_FillRect) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.LCD_FillRect) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.LCD_SetAddress) refers to lcd_spi_130.o(i.LCD_WriteCommand) for LCD_WriteCommand
    lcd_spi_130.o(i.LCD_SetAddress) refers to lcd_spi_130.o(i.LCD_WriteData_16bit) for LCD_WriteData_16bit
    lcd_spi_130.o(i.LCD_SetAddress) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_SetAsciiFont) refers to lcd_spi_130.o(.data) for LCD_AsciiFonts
    lcd_spi_130.o(i.LCD_SetBackColor) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_SetColor) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_SetDirection) refers to lcd_spi_130.o(i.LCD_WriteCommand) for LCD_WriteCommand
    lcd_spi_130.o(i.LCD_SetDirection) refers to lcd_spi_130.o(i.LCD_WriteData_8bit) for LCD_WriteData_8bit
    lcd_spi_130.o(i.LCD_SetDirection) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_SetTextFont) refers to lcd_spi_130.o(.data) for LCD_CHFonts
    lcd_spi_130.o(i.LCD_SetTextFont) refers to lcd_fonts.o(.data) for ASCII_Font12
    lcd_spi_130.o(i.LCD_ShowNumMode) refers to lcd_spi_130.o(.bss) for LCD
    lcd_spi_130.o(i.LCD_WriteBuff) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.LCD_WriteCommand) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_spi_130.o(i.LCD_WriteCommand) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.LCD_WriteData_16bit) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.LCD_WriteData_8bit) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.MX_SPI3_Init) refers to stm32f4xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    lcd_spi_130.o(i.MX_SPI3_Init) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.HAL_SPI3_MspInit) for HAL_SPI3_MspInit
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.MX_SPI3_Init) for MX_SPI3_Init
    lcd_spi_130.o(i.SPI_LCD_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_WriteCommand) for LCD_WriteCommand
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_WriteData_8bit) for LCD_WriteData_8bit
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_SetDirection) for LCD_SetDirection
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_SetBackColor) for LCD_SetBackColor
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_SetColor) for LCD_SetColor
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_Clear) for LCD_Clear
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_SetAsciiFont) for LCD_SetAsciiFont
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(i.LCD_ShowNumMode) for LCD_ShowNumMode
    lcd_spi_130.o(i.SPI_LCD_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_spi_130.o(.bss) for hspi3
    lcd_spi_130.o(i.SPI_LCD_Init) refers to lcd_fonts.o(.data) for ASCII_Font24
    lcd_spi_130.o(i.ips200_showimage) refers to lcd_spi_130.o(i.LCD_SetAddress) for LCD_SetAddress
    lcd_spi_130.o(i.ips200_showimage) refers to lcd_spi_130.o(i.LCD_WriteBuff) for LCD_WriteBuff
    atk_ms901m.o(i.atk_ms901m_get_attitude) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_get_attitude) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_get_barometer) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_get_barometer) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_get_frame_by_id) refers to atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_read) for atk_ms901m_uart_rx_fifo_read
    atk_ms901m.o(i.atk_ms901m_get_frame_by_id) refers to delay.o(i.delay_us) for delay_us
    atk_ms901m.o(i.atk_ms901m_get_gyro_accelerometer) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_get_gyro_accelerometer) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_get_gyro_accelerometer) refers to atk_ms901m.o(.data) for g_atk_ms901m_fsr
    atk_ms901m.o(i.atk_ms901m_get_gyro_accelerometer) refers to atk_ms901m.o(.constdata) for g_atk_ms901m_gyro_fsr_table
    atk_ms901m.o(i.atk_ms901m_get_led_state) refers to atk_ms901m.o(i.atk_ms901m_read_reg_by_id) for atk_ms901m_read_reg_by_id
    atk_ms901m.o(i.atk_ms901m_get_magnetometer) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_get_magnetometer) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_get_port) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_get_port) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_get_port_mode) refers to atk_ms901m.o(i.atk_ms901m_read_reg_by_id) for atk_ms901m_read_reg_by_id
    atk_ms901m.o(i.atk_ms901m_get_port_pwm_period) refers to atk_ms901m.o(i.atk_ms901m_read_reg_by_id) for atk_ms901m_read_reg_by_id
    atk_ms901m.o(i.atk_ms901m_get_port_pwm_pulse) refers to atk_ms901m.o(i.atk_ms901m_read_reg_by_id) for atk_ms901m_read_reg_by_id
    atk_ms901m.o(i.atk_ms901m_get_quaternion) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_get_quaternion) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_init) refers to atk_ms901m_uart.o(i.atk_ms901m_uart_init) for atk_ms901m_uart_init
    atk_ms901m.o(i.atk_ms901m_init) refers to atk_ms901m.o(i.atk_ms901m_read_reg_by_id) for atk_ms901m_read_reg_by_id
    atk_ms901m.o(i.atk_ms901m_init) refers to atk_ms901m.o(.data) for g_atk_ms901m_fsr
    atk_ms901m.o(i.atk_ms901m_read_reg_by_id) refers to memseta.o(.text) for __aeabi_memclr4
    atk_ms901m.o(i.atk_ms901m_read_reg_by_id) refers to atk_ms901m_uart.o(i.atk_ms901m_uart_send) for atk_ms901m_uart_send
    atk_ms901m.o(i.atk_ms901m_read_reg_by_id) refers to atk_ms901m.o(i.atk_ms901m_get_frame_by_id) for atk_ms901m_get_frame_by_id
    atk_ms901m.o(i.atk_ms901m_set_led_state) refers to atk_ms901m.o(i.atk_ms901m_write_reg_by_id) for atk_ms901m_write_reg_by_id
    atk_ms901m.o(i.atk_ms901m_set_led_state) refers to atk_ms901m.o(i.atk_ms901m_get_led_state) for atk_ms901m_get_led_state
    atk_ms901m.o(i.atk_ms901m_set_port_mode) refers to atk_ms901m.o(i.atk_ms901m_write_reg_by_id) for atk_ms901m_write_reg_by_id
    atk_ms901m.o(i.atk_ms901m_set_port_mode) refers to atk_ms901m.o(i.atk_ms901m_get_port_mode) for atk_ms901m_get_port_mode
    atk_ms901m.o(i.atk_ms901m_set_port_pwm_period) refers to atk_ms901m.o(i.atk_ms901m_write_reg_by_id) for atk_ms901m_write_reg_by_id
    atk_ms901m.o(i.atk_ms901m_set_port_pwm_period) refers to atk_ms901m.o(i.atk_ms901m_get_port_pwm_period) for atk_ms901m_get_port_pwm_period
    atk_ms901m.o(i.atk_ms901m_set_port_pwm_pulse) refers to atk_ms901m.o(i.atk_ms901m_write_reg_by_id) for atk_ms901m_write_reg_by_id
    atk_ms901m.o(i.atk_ms901m_set_port_pwm_pulse) refers to atk_ms901m.o(i.atk_ms901m_get_port_pwm_pulse) for atk_ms901m_get_port_pwm_pulse
    atk_ms901m.o(i.atk_ms901m_write_reg_by_id) refers to atk_ms901m_uart.o(i.atk_ms901m_uart_send) for atk_ms901m_uart_send
    atk_ms901m_uart.o(i.atk_ms901m_rx_fifo_flush) refers to atk_ms901m_uart.o(.bss) for g_uart_rx_fifo
    atk_ms901m_uart.o(i.atk_ms901m_uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    atk_ms901m_uart.o(i.atk_ms901m_uart_init) refers to main.o(i.Error_Handler) for Error_Handler
    atk_ms901m_uart.o(i.atk_ms901m_uart_init) refers to atk_ms901m_uart.o(.bss) for huart3
    atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_read) refers to atk_ms901m_uart.o(.bss) for g_uart_rx_fifo
    atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_write) refers to atk_ms901m_uart.o(.bss) for g_uart_rx_fifo
    atk_ms901m_uart.o(i.atk_ms901m_uart_send) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    atk_ms901m_uart.o(i.atk_ms901m_uart_send) refers to atk_ms901m_uart.o(.bss) for huart3
    delay.o(i.delay_us) refers to delay.o(.data) for g_fac_us
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    eeprom_emul.o(i.EE_Init) refers to eeprom_emul.o(i.Flash_ErasePage) for Flash_ErasePage
    eeprom_emul.o(i.EE_Init) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    eeprom_emul.o(i.EE_Init) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    eeprom_emul.o(i.EE_Init) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    eeprom_emul.o(i.EE_Read) refers to eeprom_emul.o(i.FindActivePage) for FindActivePage
    eeprom_emul.o(i.EE_Write) refers to eeprom_emul.o(i.FindActivePage) for FindActivePage
    eeprom_emul.o(i.EE_Write) refers to eeprom_emul.o(i.FindFreeRecordAddr) for FindFreeRecordAddr
    eeprom_emul.o(i.EE_Write) refers to eeprom_emul.o(i.PageTransfer) for PageTransfer
    eeprom_emul.o(i.EE_Write) refers to eeprom_emul.o(i.Flash_ProgramWord) for Flash_ProgramWord
    eeprom_emul.o(i.FindActivePage) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    eeprom_emul.o(i.FindActivePage) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    eeprom_emul.o(i.FindActivePage) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    eeprom_emul.o(i.Flash_ErasePage) refers to eeprom_emul.o(i.GetSector) for GetSector
    eeprom_emul.o(i.Flash_ErasePage) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    eeprom_emul.o(i.Flash_ErasePage) refers to stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    eeprom_emul.o(i.Flash_ErasePage) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    eeprom_emul.o(i.Flash_ProgramWord) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    eeprom_emul.o(i.Flash_ProgramWord) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    eeprom_emul.o(i.Flash_ProgramWord) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    eeprom_emul.o(i.PageTransfer) refers to eeprom_emul.o(i.Flash_ErasePage) for Flash_ErasePage
    eeprom_emul.o(i.PageTransfer) refers to eeprom_emul.o(i.Flash_ProgramWord) for Flash_ProgramWord
    eeprom_emul.o(i.PageTransfer) refers to memseta.o(.text) for __aeabi_memset
    flash.o(i.Flash_Init) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_Init) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    flash.o(i.Flash_Init) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    flash.o(i.Flash_Write) refers to flash.o(i.FindEmptySlot) for FindEmptySlot
    flash.o(i.Flash_Write) refers to flash.o(i.Flash_WriteRecord) for Flash_WriteRecord
    flash.o(i.Flash_Write) refers to flash.o(.constdata) for .constdata
    flash.o(i.Flash_WriteRecord) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    flash.o(i.Flash_WriteRecord) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    flash.o(i.Flash_WriteRecord) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    asin.o(i.__hardfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.SHURUBUHUO), (76 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (80 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (48 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (124 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (84 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (280 bytes).
    Removing usart.o(i.fgetc), (28 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt), (126 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (14 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (136 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (68 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (354 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (128 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (444 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (348 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (72 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (128 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (88 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (110 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (26 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (14 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (552 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (70 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (172 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (288 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (300 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (104 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (116 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (96 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (324 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (112 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (244 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (416 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (384 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (112 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (36 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (100 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (148 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (4056 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (52 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (170 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (576 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (236 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (120 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (80 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (474 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (440 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (126 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (780 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (676 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (548 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (288 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (368 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (232 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (776 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (612 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (264 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (356 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (540 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (384 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (80 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (18 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (428 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (62 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (312 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (192 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (160 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (216 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10), (42 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (616 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (198 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (224 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (112 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (102 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (250 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (138 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT), (80 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (102 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort), (368 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Abort_IT), (384 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAPause), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAResume), (50 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DMAStop), (72 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_DeInit), (54 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetError), (6 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_GetState), (8 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_IRQHandler), (348 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive), (386 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (280 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Receive_IT), (212 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit), (446 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (584 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (356 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (184 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (252 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (176 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (52 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (52 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (52 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (52 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortRx_ISR), (112 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_AbortTx_ISR), (32 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (188 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseRx_ISR), (94 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_CloseTx_ISR), (160 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAAbortOnError), (20 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAError), (40 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (14 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (14 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (14 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMAReceiveCplt), (124 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMARxAbortCallback), (144 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitCplt), (128 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (104 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_DMATxAbortCallback), (172 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTransaction), (148 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction), (136 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_16BIT), (36 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_RxISR_8BIT), (36 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_16BIT), (36 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_TxISR_8BIT), (36 bytes).
    Removing stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (216 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (156 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (278 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (556 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (214 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (256 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (316 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (608 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (400 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (310 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (184 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (178 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (164 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (216 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (228 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (80 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (158 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (196 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (126 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (336 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (166 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (170 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (138 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing inv_mpu.o(.rev16_text), (4 bytes).
    Removing inv_mpu.o(.revsh_text), (4 bytes).
    Removing inv_mpu.o(.rrx_text), (6 bytes).
    Removing inv_mpu.o(i.accel_self_test), (192 bytes).
    Removing inv_mpu.o(i.get_accel_prod_shift), (208 bytes).
    Removing inv_mpu.o(i.get_ms), (2 bytes).
    Removing inv_mpu.o(i.get_st_biases), (1132 bytes).
    Removing inv_mpu.o(i.gyro_self_test), (284 bytes).
    Removing inv_mpu.o(i.mpu_configure_fifo), (112 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_fsr), (76 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (116 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_sens), (84 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (10 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_fsr), (68 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (116 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_sens), (96 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (68 bytes).
    Removing inv_mpu.o(i.mpu_get_lpf), (80 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (28 bytes).
    Removing inv_mpu.o(i.mpu_get_sample_rate), (32 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (156 bytes).
    Removing inv_mpu.o(i.mpu_init), (252 bytes).
    Removing inv_mpu.o(i.mpu_load_firmware), (184 bytes).
    Removing inv_mpu.o(i.mpu_lp_accel_mode), (212 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (236 bytes).
    Removing inv_mpu.o(i.mpu_read_6050_accel_bias), (128 bytes).
    Removing inv_mpu.o(i.mpu_read_6500_accel_bias), (128 bytes).
    Removing inv_mpu.o(i.mpu_read_6500_gyro_bias), (128 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (504 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo_stream), (192 bytes).
    Removing inv_mpu.o(i.mpu_read_mem), (128 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (104 bytes).
    Removing inv_mpu.o(i.mpu_reset_fifo), (456 bytes).
    Removing inv_mpu.o(i.mpu_run_self_test), (288 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias_6050_reg), (200 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias_6500_reg), (200 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_fsr), (132 bytes).
    Removing inv_mpu.o(i.mpu_set_bypass), (332 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_set_dmp_state), (144 bytes).
    Removing inv_mpu.o(i.mpu_set_gyro_bias_reg), (188 bytes).
    Removing inv_mpu.o(i.mpu_set_gyro_fsr), (136 bytes).
    Removing inv_mpu.o(i.mpu_set_int_latched), (108 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(i.mpu_set_lpf), (132 bytes).
    Removing inv_mpu.o(i.mpu_set_sample_rate), (156 bytes).
    Removing inv_mpu.o(i.mpu_set_sensors), (208 bytes).
    Removing inv_mpu.o(i.mpu_write_mem), (128 bytes).
    Removing inv_mpu.o(i.set_int_enable), (144 bytes).
    Removing inv_mpu.o(.constdata), (80 bytes).
    Removing inv_mpu.o(.data), (44 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rev16_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.revsh_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rrx_text), (6 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.decode_gesture), (100 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat), (60 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature), (540 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal), (88 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat), (60 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (66 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (72 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware), (24 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo), (460 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias), (304 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate), (112 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias), (304 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (92 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation), (312 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (42 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (48 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh), (56 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes), (70 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh), (448 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.constdata), (3062 bytes).
    Removing my_mpu6050.o(.rev16_text), (4 bytes).
    Removing my_mpu6050.o(.revsh_text), (4 bytes).
    Removing my_mpu6050.o(.rrx_text), (6 bytes).
    Removing my_mpu6050.o(i.MPU6050_Read_Temp), (108 bytes).
    Removing my_mpu6050.o(i.SYSTEM_Reset), (52 bytes).
    Removing my_mpu6050.o(i.dmp_getdata), (720 bytes).
    Removing my_mpu6050.o(i.gyro_data_ready_cb), (12 bytes).
    Removing my_mpu6050.o(i.i2c2_read), (56 bytes).
    Removing my_mpu6050.o(i.i2c2_write), (56 bytes).
    Removing my_mpu6050.o(i.inv_row_2_scale), (78 bytes).
    Removing my_mpu6050.o(i.mpu_dmp_init), (192 bytes).
    Removing my_mpu6050.o(.bss), (66 bytes).
    Removing my_mpu6050.o(.data), (40 bytes).
    Removing nrf24l01.o(.rev16_text), (4 bytes).
    Removing nrf24l01.o(.revsh_text), (4 bytes).
    Removing nrf24l01.o(.rrx_text), (6 bytes).
    Removing nrf24l01.o(i.NRF24L01_Check), (132 bytes).
    Removing nrf24l01.o(i.NRF24L01_Init), (36 bytes).
    Removing nrf24l01.o(i.NRF24L01_Read_Buf), (68 bytes).
    Removing nrf24l01.o(i.NRF24L01_Read_Reg), (48 bytes).
    Removing nrf24l01.o(i.NRF24L01_RxPacket), (104 bytes).
    Removing nrf24l01.o(i.NRF24L01_TxPacket), (172 bytes).
    Removing nrf24l01.o(i.NRF24L01_Write_Buf), (68 bytes).
    Removing nrf24l01.o(i.NRF24L01_Write_Reg), (52 bytes).
    Removing nrf24l01.o(i.NRF_delay_us), (26 bytes).
    Removing nrf24l01.o(i.RX_Mode), (92 bytes).
    Removing nrf24l01.o(i.TX_Mode), (100 bytes).
    Removing nrf24l01.o(i.nRF24_SPI_Send_Byte), (32 bytes).
    Removing nrf24l01.o(.constdata), (30 bytes).
    Removing nrf24l01.o(.data), (2 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.Key_Scam), (220 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing motor.o(i.BACK), (100 bytes).
    Removing motor.o(i.FORWARD), (100 bytes).
    Removing motor.o(i.LEFT), (68 bytes).
    Removing motor.o(i.RIGHT), (68 bytes).
    Removing motor.o(i.STOP), (80 bytes).
    Removing motor.o(i.target), (28 bytes).
    Removing motor.o(.data), (52 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (168 bytes).
    Removing oled.o(i.OLED_DrawLine), (172 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (172 bytes).
    Removing oled.o(i.OLED_ShowChinese), (260 bytes).
    Removing oled.o(i.OLED_ShowPicture), (214 bytes).
    Removing oled.o(i.OLED_ShowString), (74 bytes).
    Removing exit_usart.o(.rev16_text), (4 bytes).
    Removing exit_usart.o(.revsh_text), (4 bytes).
    Removing exit_usart.o(.rrx_text), (6 bytes).
    Removing exit_usart.o(.constdata), (4 bytes).
    Removing lcd_image.o(.constdata), (3652 bytes).
    Removing lcd_spi_130.o(.rev16_text), (4 bytes).
    Removing lcd_spi_130.o(.revsh_text), (4 bytes).
    Removing lcd_spi_130.o(.rrx_text), (6 bytes).
    Removing lcd_spi_130.o(i.LCD_ClearRect), (232 bytes).
    Removing lcd_spi_130.o(i.LCD_DisplayChar), (228 bytes).
    Removing lcd_spi_130.o(i.LCD_DisplayChinese), (288 bytes).
    Removing lcd_spi_130.o(i.LCD_DisplayDecimals), (104 bytes).
    Removing lcd_spi_130.o(i.LCD_DisplayNumber), (76 bytes).
    Removing lcd_spi_130.o(i.LCD_DisplayString), (56 bytes).
    Removing lcd_spi_130.o(i.LCD_DisplayText), (80 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawCircle), (148 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawEllipse), (552 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawImage), (304 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawLine), (300 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawLine_H), (68 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawLine_V), (68 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawPoint), (76 bytes).
    Removing lcd_spi_130.o(i.LCD_DrawRect), (64 bytes).
    Removing lcd_spi_130.o(i.LCD_FillCircle), (138 bytes).
    Removing lcd_spi_130.o(i.LCD_FillRect), (232 bytes).
    Removing lcd_spi_130.o(i.LCD_SetTextFont), (108 bytes).
    Removing atk_ms901m.o(.rev16_text), (4 bytes).
    Removing atk_ms901m.o(.revsh_text), (4 bytes).
    Removing atk_ms901m.o(.rrx_text), (6 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_attitude), (184 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_barometer), (148 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_frame_by_id), (416 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_gyro_accelerometer), (440 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_led_state), (28 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_magnetometer), (136 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_port), (104 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_port_mode), (68 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_port_pwm_period), (68 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_port_pwm_pulse), (68 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_get_quaternion), (188 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_init), (56 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_read_reg_by_id), (150 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_set_led_state), (60 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_set_port_mode), (124 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_set_port_pwm_period), (100 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_set_port_pwm_pulse), (100 bytes).
    Removing atk_ms901m.o(i.atk_ms901m_write_reg_by_id), (156 bytes).
    Removing atk_ms901m.o(.constdata), (12 bytes).
    Removing atk_ms901m.o(.data), (2 bytes).
    Removing atk_ms901m_uart.o(.rev16_text), (4 bytes).
    Removing atk_ms901m_uart.o(.revsh_text), (4 bytes).
    Removing atk_ms901m_uart.o(.rrx_text), (6 bytes).
    Removing atk_ms901m_uart.o(i.atk_ms901m_rx_fifo_flush), (20 bytes).
    Removing atk_ms901m_uart.o(i.atk_ms901m_uart_init), (76 bytes).
    Removing atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_read), (128 bytes).
    Removing atk_ms901m_uart.o(i.atk_ms901m_uart_send), (28 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.delay_us), (72 bytes).
    Removing delay.o(.data), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_intx_disable), (4 bytes).
    Removing sys.o(i.sys_intx_enable), (4 bytes).
    Removing sys.o(i.sys_msr_msp), (10 bytes).
    Removing sys.o(i.sys_nvic_set_vector_table), (16 bytes).
    Removing sys.o(i.sys_soft_reset), (44 bytes).
    Removing sys.o(i.sys_standby), (56 bytes).
    Removing sys.o(i.sys_stm32_clock_init), (204 bytes).
    Removing sys.o(i.sys_wfi_set), (4 bytes).
    Removing eeprom_emul.o(.rev16_text), (4 bytes).
    Removing eeprom_emul.o(.revsh_text), (4 bytes).
    Removing eeprom_emul.o(.rrx_text), (6 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing f2d.o(.text), (38 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing dsqrt.o(.text), (162 bytes).

801 unused section(s) (total 94522 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c 0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\HARDWARED\NRF24L01.c                  0x00000000   Number         0  nrf24l01.o ABSOLUTE
    ..\HARDWARED\exit_usart.c                0x00000000   Number         0  exit_usart.o ABSOLUTE
    ..\HARDWARED\key.c                       0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARED\motor.c                     0x00000000   Number         0  motor.o ABSOLUTE
    ..\HARDWARED\oled.c                      0x00000000   Number         0  oled.o ABSOLUTE
    ..\IMU901\atk_ms901m.c                   0x00000000   Number         0  atk_ms901m.o ABSOLUTE
    ..\IMU901\atk_ms901m_uart.c              0x00000000   Number         0  atk_ms901m_uart.o ABSOLUTE
    ..\IMU901\delay.c                        0x00000000   Number         0  delay.o ABSOLUTE
    ..\IMU901\sys.c                          0x00000000   Number         0  sys.o ABSOLUTE
    ..\IPS\Src\lcd_fonts.c                   0x00000000   Number         0  lcd_fonts.o ABSOLUTE
    ..\IPS\Src\lcd_image.c                   0x00000000   Number         0  lcd_image.o ABSOLUTE
    ..\IPS\Src\lcd_spi_130.c                 0x00000000   Number         0  lcd_spi_130.o ABSOLUTE
    ..\USER\inv_mpu.c                        0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\USER\inv_mpu_dmp_motion_driver.c      0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\USER\my_mpu6050.c                     0x00000000   Number         0  my_mpu6050.o ABSOLUTE
    ..\\HARDWARED\\NRF24L01.c                0x00000000   Number         0  nrf24l01.o ABSOLUTE
    ..\\HARDWARED\\exit_usart.c              0x00000000   Number         0  exit_usart.o ABSOLUTE
    ..\\HARDWARED\\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARED\\motor.c                   0x00000000   Number         0  motor.o ABSOLUTE
    ..\\HARDWARED\\oled.c                    0x00000000   Number         0  oled.o ABSOLUTE
    ..\\IMU901\\atk_ms901m.c                 0x00000000   Number         0  atk_ms901m.o ABSOLUTE
    ..\\IMU901\\atk_ms901m_uart.c            0x00000000   Number         0  atk_ms901m_uart.o ABSOLUTE
    ..\\IMU901\\delay.c                      0x00000000   Number         0  delay.o ABSOLUTE
    ..\\IMU901\\sys.c                        0x00000000   Number         0  sys.o ABSOLUTE
    ..\\IPS\\Src\\lcd_spi_130.c              0x00000000   Number         0  lcd_spi_130.o ABSOLUTE
    ..\\USER\\inv_mpu.c                      0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\\USER\\inv_mpu_dmp_motion_driver.c    0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\\USER\\my_mpu6050.c                   0x00000000   Number         0  my_mpu6050.o ABSOLUTE
    ..\\eeprom\\eeprom_emul.c                0x00000000   Number         0  eeprom_emul.o ABSOLUTE
    ..\\eeprom\\flash.c                      0x00000000   Number         0  flash.o ABSOLUTE
    ..\eeprom\eeprom_emul.c                  0x00000000   Number         0  eeprom_emul.o ABSOLUTE
    ..\eeprom\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  memseta.o(.text)
    .text                                    0x0800024a   Section        0  dmul.o(.text)
    .text                                    0x0800032e   Section        0  uidiv.o(.text)
    .text                                    0x0800035a   Section        0  llshl.o(.text)
    .text                                    0x08000378   Section        0  llushr.o(.text)
    .text                                    0x08000398   Section        0  depilogue.o(.text)
    .text                                    0x08000398   Section        0  iusefp.o(.text)
    .text                                    0x08000452   Section        0  dadd.o(.text)
    .text                                    0x080005a0   Section        0  ddiv.o(.text)
    .text                                    0x0800067e   Section        0  dfixul.o(.text)
    .text                                    0x080006b0   Section       48  cdrcmple.o(.text)
    .text                                    0x080006e0   Section       36  init.o(.text)
    .text                                    0x08000704   Section        0  llsshr.o(.text)
    i.ADC_Init                               0x08000728   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08000729   Thumb Code   344  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x08000888   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA_SetConfig                          0x0800088c   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x0800088d   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080008b8   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.EE_Init                                0x080008bc   Section        0  eeprom_emul.o(i.EE_Init)
    i.EE_Read                                0x08000968   Section        0  eeprom_emul.o(i.EE_Read)
    i.EE_Write                               0x080009b0   Section        0  eeprom_emul.o(i.EE_Write)
    i.EXTI15_10_IRQHandler                   0x08000a2c   Section        0  stm32f4xx_it.o(i.EXTI15_10_IRQHandler)
    i.Error_Handler                          0x08000a38   Section        0  main.o(i.Error_Handler)
    i.FLASH_Erase_Sector                     0x08000a40   Section        0  stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector)
    i.FLASH_FlushCaches                      0x08000a9c   Section        0  stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches)
    i.FLASH_MassErase                        0x08000b14   Section        0  stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase)
    FLASH_MassErase                          0x08000b15   Thumb Code    42  stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase)
    i.FLASH_Program_Byte                     0x08000b44   Section        0  stm32f4xx_hal_flash.o(i.FLASH_Program_Byte)
    FLASH_Program_Byte                       0x08000b45   Thumb Code    32  stm32f4xx_hal_flash.o(i.FLASH_Program_Byte)
    i.FLASH_Program_DoubleWord               0x08000b68   Section        0  stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord)
    FLASH_Program_DoubleWord                 0x08000b69   Thumb Code    44  stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord)
    i.FLASH_Program_HalfWord                 0x08000b98   Section        0  stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord)
    FLASH_Program_HalfWord                   0x08000b99   Thumb Code    36  stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord)
    i.FLASH_Program_Word                     0x08000bc0   Section        0  stm32f4xx_hal_flash.o(i.FLASH_Program_Word)
    FLASH_Program_Word                       0x08000bc1   Thumb Code    36  stm32f4xx_hal_flash.o(i.FLASH_Program_Word)
    i.FLASH_SetErrorCode                     0x08000be8   Section        0  stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode)
    FLASH_SetErrorCode                       0x08000be9   Thumb Code   142  stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode)
    i.FLASH_WaitForLastOperation             0x08000c80   Section        0  stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    i.FindActivePage                         0x08000ce4   Section        0  eeprom_emul.o(i.FindActivePage)
    FindActivePage                           0x08000ce5   Thumb Code    86  eeprom_emul.o(i.FindActivePage)
    i.FindEmptySlot                          0x08000d44   Section        0  flash.o(i.FindEmptySlot)
    FindEmptySlot                            0x08000d45   Thumb Code    30  flash.o(i.FindEmptySlot)
    i.FindFreeRecordAddr                     0x08000d62   Section        0  eeprom_emul.o(i.FindFreeRecordAddr)
    FindFreeRecordAddr                       0x08000d63   Thumb Code    36  eeprom_emul.o(i.FindFreeRecordAddr)
    i.Flash_ErasePage                        0x08000d86   Section        0  eeprom_emul.o(i.Flash_ErasePage)
    Flash_ErasePage                          0x08000d87   Thumb Code    56  eeprom_emul.o(i.Flash_ErasePage)
    i.Flash_Init                             0x08000dc0   Section        0  flash.o(i.Flash_Init)
    i.Flash_ProgramWord                      0x08000dec   Section        0  eeprom_emul.o(i.Flash_ProgramWord)
    Flash_ProgramWord                        0x08000ded   Thumb Code    32  eeprom_emul.o(i.Flash_ProgramWord)
    i.Flash_Read                             0x08000e0c   Section        0  flash.o(i.Flash_Read)
    i.Flash_Write                            0x08000e44   Section        0  flash.o(i.Flash_Write)
    i.Flash_WriteRecord                      0x08000e7c   Section        0  flash.o(i.Flash_WriteRecord)
    Flash_WriteRecord                        0x08000e7d   Thumb Code    60  flash.o(i.Flash_WriteRecord)
    i.GetSector                              0x08000eb8   Section        0  eeprom_emul.o(i.GetSector)
    GetSector                                0x08000eb9   Thumb Code   116  eeprom_emul.o(i.GetSector)
    i.HAL_ADC_ConfigChannel                  0x08000f58   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_GetState                       0x080010e4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_GetState)
    i.HAL_ADC_GetValue                       0x080010ea   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    i.HAL_ADC_Init                           0x080010f4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x0800115c   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_PollForConversion              0x08001240   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    i.HAL_ADC_Start                          0x08001310   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start)
    i.HAL_DMA_Abort                          0x0800145c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08001508   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_Start_IT                       0x08001530   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x080015c4   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_FLASHEx_Erase                      0x080015ec   Section        0  stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    i.HAL_FLASH_Lock                         0x08001698   Section        0  stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock)
    i.HAL_FLASH_Program                      0x080016ac   Section        0  stm32f4xx_hal_flash.o(i.HAL_FLASH_Program)
    i.HAL_FLASH_Unlock                       0x0800173c   Section        0  stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock)
    i.HAL_GPIO_EXTI_Callback                 0x08001770   Section        0  main.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080017f4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x08001810   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08001a04   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001a10   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08001a1c   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_MspInit                        0x08001bec   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08001c74   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001c8c   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001cc8   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001d14   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001d5c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001d84   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001e00   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001e28   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08001fac   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08001fb8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08001fd8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08001ff8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080020a8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI3_MspInit                       0x08002544   Section        0  lcd_spi_130.o(i.HAL_SPI3_MspInit)
    i.HAL_SPI_Init                           0x080026b0   Section        0  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08002778   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SYSTICK_Config                     0x08002800   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08002834   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08002836   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08002838   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080028ac   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08002960   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080029c8   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002a98   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08002b40   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08002c4c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08002d14   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08002e08   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08002ed4   Section        0  main.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08002fb8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08003124   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080031d4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x080031d6   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080032da   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003340   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08003342   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08003344   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08003450   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_ReadCapturedValue              0x08003464   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    i.HAL_TIM_TriggerCallback                0x08003496   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08003498   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08003508   Section        0  main.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08003558   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800355c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08003864   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080038dc   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive                       0x08003c30   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive)
    i.HAL_UART_Receive_IT                    0x08003cf2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08003d20   Section        0  main.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08003dbc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08003dbe   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08003e7c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08003e7e   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Start                              0x08003e84   Section        0  oled.o(i.I2C_Start)
    i.I2C_Stop                               0x08003ec4   Section        0  oled.o(i.I2C_Stop)
    i.I2C_WaitAck                            0x08003ef0   Section        0  oled.o(i.I2C_WaitAck)
    i.IIC_delay                              0x08003f24   Section        0  oled.o(i.IIC_delay)
    i.K210                                   0x08003f34   Section        0  exit_usart.o(i.K210)
    i.LCD_Clear                              0x08003fcc   Section        0  lcd_spi_130.o(i.LCD_Clear)
    i.LCD_SetAddress                         0x080040a8   Section        0  lcd_spi_130.o(i.LCD_SetAddress)
    i.LCD_SetAsciiFont                       0x08004128   Section        0  lcd_spi_130.o(i.LCD_SetAsciiFont)
    i.LCD_SetBackColor                       0x08004134   Section        0  lcd_spi_130.o(i.LCD_SetBackColor)
    i.LCD_SetColor                           0x0800415c   Section        0  lcd_spi_130.o(i.LCD_SetColor)
    i.LCD_SetDirection                       0x08004184   Section        0  lcd_spi_130.o(i.LCD_SetDirection)
    i.LCD_ShowNumMode                        0x0800423c   Section        0  lcd_spi_130.o(i.LCD_ShowNumMode)
    i.LCD_WriteBuff                          0x08004248   Section        0  lcd_spi_130.o(i.LCD_WriteBuff)
    i.LCD_WriteCommand                       0x08004300   Section        0  lcd_spi_130.o(i.LCD_WriteCommand)
    i.LCD_WriteData_16bit                    0x0800435c   Section        0  lcd_spi_130.o(i.LCD_WriteData_16bit)
    i.LCD_WriteData_8bit                     0x08004390   Section        0  lcd_spi_130.o(i.LCD_WriteData_8bit)
    i.MKF_K210                               0x080043ac   Section        0  exit_usart.o(i.MKF_K210)
    i.MX_ADC1_Init                           0x08004430   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_ADC2_Init                           0x080044a0   Section        0  adc.o(i.MX_ADC2_Init)
    i.MX_GPIO_Init                           0x08004510   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x080046dc   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_SPI1_Init                           0x08004718   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_SPI3_Init                           0x0800475c   Section        0  lcd_spi_130.o(i.MX_SPI3_Init)
    i.MX_TIM1_Init                           0x080047bc   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x080048a4   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x0800490c   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08004988   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_TIM6_Init                           0x08004a04   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_TIM8_Init                           0x08004a50   Section        0  tim.o(i.MX_TIM8_Init)
    i.MX_UART4_Init                          0x08004b38   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x08004b70   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08004ba8   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004be0   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004c18   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MX_USART6_UART_Init                    0x08004c50   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08004c88   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004c8c   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08004c90   Section        0  oled.o(i.OLED_Clear)
    i.OLED_DrawPoint                         0x08004cc0   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_Init                              0x08004d38   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08004e16   Section        0  oled.o(i.OLED_Pow)
    i.OLED_Refresh                           0x08004e2c   Section        0  oled.o(i.OLED_Refresh)
    i.OLED_ShowChar                          0x08004e94   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowNum                           0x08004fd0   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_WR_Byte                           0x0800505c   Section        0  oled.o(i.OLED_WR_Byte)
    i.PageTransfer                           0x08005094   Section        0  eeprom_emul.o(i.PageTransfer)
    PageTransfer                             0x08005095   Thumb Code   244  eeprom_emul.o(i.PageTransfer)
    i.PendSV_Handler                         0x08005188   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SEG_GPIO_Init                          0x0800518c   Section        0  main.o(i.SEG_GPIO_Init)
    i.SEG_Refresh                            0x08005218   Section        0  main.o(i.SEG_Refresh)
    i.SEG_SetChar                            0x080052c0   Section        0  main.o(i.SEG_SetChar)
    i.SEG_ShowNumber                         0x08005310   Section        0  main.o(i.SEG_ShowNumber)
    i.SPI_LCD_Init                           0x080053a4   Section        0  lcd_spi_130.o(i.SPI_LCD_Init)
    i.SVC_Handler                            0x08005580   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Send_Byte                              0x08005584   Section        0  oled.o(i.Send_Byte)
    i.SysTick_Handler                        0x080055dc   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080055e4   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x0800569c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TFLUNA                                 0x080056b0   Section        0  exit_usart.o(i.TFLUNA)
    i.TIM6_DAC_IRQHandler                    0x0800577c   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM_Base_SetConfig                     0x0800578c   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x0800586c   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x0800588e   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080058a4   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080058a5   Thumb Code    18  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080058b8   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080058b9   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005928   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x080059a4   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x080059a5   Thumb Code   112  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08005a1c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08005a1d   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08005a70   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08005a71   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005a96   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005a97   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08005abe   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005abf   Thumb Code    18  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08005ad0   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08005ad1   Thumb Code    80  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08005b20   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005b21   Thumb Code   180  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08005bd4   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08005bd5   Thumb Code    36  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08005bf8   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005bf9   Thumb Code   108  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08005c64   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08005c65   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08005c84   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005c85   Thumb Code    38  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005caa   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005cab   Thumb Code   252  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005da8   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005da9   Thumb Code   546  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08005fd4   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x080060ac   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x080060ec   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080060ed   Thumb Code    96  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x0800614c   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x0800614d   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080061d8   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080061e8   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x080061f8   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x0800625c   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.UsageFault_Handler                     0x0800626c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08006270   Section        0  printfa.o(i.__0printf)
    i.__NVIC_GetPriorityGrouping             0x08006290   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08006291   Thumb Code    10  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x080062a0   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080062a1   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x080062c8   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080062d6   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080062d8   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080062e8   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080062e9   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0800646c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800646d   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08006b20   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08006b21   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08006b44   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08006b45   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.atk_ms901m_uart_rx_fifo_write          0x08006b74   Section        0  atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_write)
    i.fputc                                  0x08006bb8   Section        0  usart.o(i.fputc)
    i.ips200_showimage                       0x08006bd4   Section        0  lcd_spi_130.o(i.ips200_showimage)
    i.main                                   0x08006c28   Section        0  main.o(i.main)
    .constdata                               0x08006f48   Section    86416  main.o(.constdata)
    .constdata                               0x0801c0d8   Section       24  system_stm32f4xx.o(.constdata)
    .constdata                               0x0801c0f0   Section     7696  oled.o(.constdata)
    .constdata                               0x0801df00   Section    26660  lcd_fonts.o(.constdata)
    .constdata                               0x08024724   Section       12  flash.o(.constdata)
    .data                                    0x20000000   Section       81  main.o(.data)
    pos                                      0x20000050   Data           1  main.o(.data)
    .data                                    0x20000054   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x20000060   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000064   Section       24  exit_usart.o(.data)
    i3                                       0x20000076   Data           1  exit_usart.o(.data)
    j3                                       0x20000077   Data           1  exit_usart.o(.data)
    k3                                       0x20000078   Data           1  exit_usart.o(.data)
    i3                                       0x20000079   Data           1  exit_usart.o(.data)
    j3                                       0x2000007a   Data           1  exit_usart.o(.data)
    k3                                       0x2000007b   Data           1  exit_usart.o(.data)
    .data                                    0x2000007c   Section      120  lcd_fonts.o(.data)
    .data                                    0x200000f4   Section        8  lcd_spi_130.o(.data)
    LCD_AsciiFonts                           0x200000f4   Data           4  lcd_spi_130.o(.data)
    LCD_CHFonts                              0x200000f8   Data           4  lcd_spi_130.o(.data)
    .data                                    0x200000fc   Section        4  stdout.o(.data)
    .bss                                     0x20000100   Section       80  main.o(.bss)
    .bss                                     0x20000150   Section      144  adc.o(.bss)
    .bss                                     0x200001e0   Section       84  i2c.o(.bss)
    .bss                                     0x20000234   Section       88  spi.o(.bss)
    .bss                                     0x2000028c   Section      432  tim.o(.bss)
    .bss                                     0x2000043c   Section      432  usart.o(.bss)
    .bss                                     0x200005ec   Section       32  stm32f4xx_hal_flash.o(.bss)
    .bss                                     0x2000060c   Section     1152  oled.o(.bss)
    .bss                                     0x20000a8c   Section       29  exit_usart.o(.bss)
    USART1_RX_BUF                            0x20000a95   Data          10  exit_usart.o(.bss)
    USART1_RX_BUF                            0x20000a9f   Data          10  exit_usart.o(.bss)
    .bss                                     0x20000aac   Section     2152  lcd_spi_130.o(.bss)
    .bss                                     0x20001314   Section      206  atk_ms901m_uart.o(.bss)
    huart3                                   0x20001314   Data          72  atk_ms901m_uart.o(.bss)
    g_uart_rx_fifo                           0x2000135c   Data         134  atk_ms901m_uart.o(.bss)
    STACK                                    0x200013e8   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000227   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000227   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000227   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000235   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000235   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000235   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000239   Thumb Code    18  memseta.o(.text)
    __aeabi_dmul                             0x0800024b   Thumb Code   228  dmul.o(.text)
    __aeabi_uidiv                            0x0800032f   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800032f   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800035b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800035b   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000379   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000379   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000399   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000399   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080003b7   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000453   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000595   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800059b   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x080005a1   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800067f   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080006b1   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080006e1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080006e1   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x08000705   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000705   Thumb Code     0  llsshr.o(.text)
    BusFault_Handler                         0x08000889   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080008b9   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    EE_Init                                  0x080008bd   Thumb Code   162  eeprom_emul.o(i.EE_Init)
    EE_Read                                  0x08000969   Thumb Code    72  eeprom_emul.o(i.EE_Read)
    EE_Write                                 0x080009b1   Thumb Code   114  eeprom_emul.o(i.EE_Write)
    EXTI15_10_IRQHandler                     0x08000a2d   Thumb Code    12  stm32f4xx_it.o(i.EXTI15_10_IRQHandler)
    Error_Handler                            0x08000a39   Thumb Code     6  main.o(i.Error_Handler)
    FLASH_Erase_Sector                       0x08000a41   Thumb Code    86  stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector)
    FLASH_FlushCaches                        0x08000a9d   Thumb Code   114  stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches)
    FLASH_WaitForLastOperation               0x08000c81   Thumb Code    90  stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    Flash_Init                               0x08000dc1   Thumb Code    36  flash.o(i.Flash_Init)
    Flash_Read                               0x08000e0d   Thumb Code    52  flash.o(i.Flash_Read)
    Flash_Write                              0x08000e45   Thumb Code    48  flash.o(i.Flash_Write)
    HAL_ADC_ConfigChannel                    0x08000f59   Thumb Code   380  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_GetState                         0x080010e5   Thumb Code     6  stm32f4xx_hal_adc.o(i.HAL_ADC_GetState)
    HAL_ADC_GetValue                         0x080010eb   Thumb Code     8  stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue)
    HAL_ADC_Init                             0x080010f5   Thumb Code   100  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x0800115d   Thumb Code   212  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_PollForConversion                0x08001241   Thumb Code   206  stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion)
    HAL_ADC_Start                            0x08001311   Thumb Code   308  stm32f4xx_hal_adc.o(i.HAL_ADC_Start)
    HAL_DMA_Abort                            0x0800145d   Thumb Code   172  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001509   Thumb Code    40  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_Start_IT                         0x08001531   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x080015c5   Thumb Code    36  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_FLASHEx_Erase                        0x080015ed   Thumb Code   162  stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    HAL_FLASH_Lock                           0x08001699   Thumb Code    16  stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock)
    HAL_FLASH_Program                        0x080016ad   Thumb Code   134  stm32f4xx_hal_flash.o(i.HAL_FLASH_Program)
    HAL_FLASH_Unlock                         0x0800173d   Thumb Code    38  stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock)
    HAL_GPIO_EXTI_Callback                   0x08001771   Thumb Code    86  main.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080017f5   Thumb Code    24  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x08001811   Thumb Code   454  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08001a05   Thumb Code    12  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001a11   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08001a1d   Thumb Code   446  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08001bed   Thumb Code   124  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08001c75   Thumb Code    16  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001c8d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001cc9   Thumb Code    64  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001d15   Thumb Code    68  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001d5d   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001d85   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001e01   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001e29   Thumb Code   368  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08001fad   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08001fb9   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001fd9   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001ff9   Thumb Code   162  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080020a9   Thumb Code  1172  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI3_MspInit                         0x08002545   Thumb Code   350  lcd_spi_130.o(i.HAL_SPI3_MspInit)
    HAL_SPI_Init                             0x080026b1   Thumb Code   200  stm32f4xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08002779   Thumb Code   122  spi.o(i.HAL_SPI_MspInit)
    HAL_SYSTICK_Config                       0x08002801   Thumb Code    52  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08002835   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08002837   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08002839   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x080028ad   Thumb Code   150  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002961   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080029c9   Thumb Code   192  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002a99   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08002b41   Thumb Code   268  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08002c4d   Thumb Code   200  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08002d15   Thumb Code   224  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08002e09   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08002ed5   Thumb Code   214  main.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08002fb9   Thumb Code   364  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08003125   Thumb Code   156  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x080031d5   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x080031d7   Thumb Code   260  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080032db   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003341   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003343   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08003345   Thumb Code   238  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08003451   Thumb Code    16  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_ReadCapturedValue                0x08003465   Thumb Code    50  stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue)
    HAL_TIM_TriggerCallback                  0x08003497   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003499   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08003509   Thumb Code    56  main.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003559   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800355d   Thumb Code   772  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003865   Thumb Code   118  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080038dd   Thumb Code   802  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive                         0x08003c31   Thumb Code   194  stm32f4xx_hal_uart.o(i.HAL_UART_Receive)
    HAL_UART_Receive_IT                      0x08003cf3   Thumb Code    44  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08003d21   Thumb Code   108  main.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003dbd   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08003dbf   Thumb Code   190  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08003e7d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08003e7f   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    I2C_Start                                0x08003e85   Thumb Code    60  oled.o(i.I2C_Start)
    I2C_Stop                                 0x08003ec5   Thumb Code    40  oled.o(i.I2C_Stop)
    I2C_WaitAck                              0x08003ef1   Thumb Code    48  oled.o(i.I2C_WaitAck)
    IIC_delay                                0x08003f25   Thumb Code    16  oled.o(i.IIC_delay)
    K210                                     0x08003f35   Thumb Code   118  exit_usart.o(i.K210)
    LCD_Clear                                0x08003fcd   Thumb Code   208  lcd_spi_130.o(i.LCD_Clear)
    LCD_SetAddress                           0x080040a9   Thumb Code   114  lcd_spi_130.o(i.LCD_SetAddress)
    LCD_SetAsciiFont                         0x08004129   Thumb Code     6  lcd_spi_130.o(i.LCD_SetAsciiFont)
    LCD_SetBackColor                         0x08004135   Thumb Code    36  lcd_spi_130.o(i.LCD_SetBackColor)
    LCD_SetColor                             0x0800415d   Thumb Code    36  lcd_spi_130.o(i.LCD_SetColor)
    LCD_SetDirection                         0x08004185   Thumb Code   170  lcd_spi_130.o(i.LCD_SetDirection)
    LCD_ShowNumMode                          0x0800423d   Thumb Code     6  lcd_spi_130.o(i.LCD_ShowNumMode)
    LCD_WriteBuff                            0x08004249   Thumb Code   176  lcd_spi_130.o(i.LCD_WriteBuff)
    LCD_WriteCommand                         0x08004301   Thumb Code    82  lcd_spi_130.o(i.LCD_WriteCommand)
    LCD_WriteData_16bit                      0x0800435d   Thumb Code    48  lcd_spi_130.o(i.LCD_WriteData_16bit)
    LCD_WriteData_8bit                       0x08004391   Thumb Code    24  lcd_spi_130.o(i.LCD_WriteData_8bit)
    MKF_K210                                 0x080043ad   Thumb Code   104  exit_usart.o(i.MKF_K210)
    MX_ADC1_Init                             0x08004431   Thumb Code   100  adc.o(i.MX_ADC1_Init)
    MX_ADC2_Init                             0x080044a1   Thumb Code   100  adc.o(i.MX_ADC2_Init)
    MX_GPIO_Init                             0x08004511   Thumb Code   440  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x080046dd   Thumb Code    48  i2c.o(i.MX_I2C1_Init)
    MX_SPI1_Init                             0x08004719   Thumb Code    58  spi.o(i.MX_SPI1_Init)
    MX_SPI3_Init                             0x0800475d   Thumb Code    86  lcd_spi_130.o(i.MX_SPI3_Init)
    MX_TIM1_Init                             0x080047bd   Thumb Code   222  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x080048a5   Thumb Code   100  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x0800490d   Thumb Code   114  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004989   Thumb Code   114  tim.o(i.MX_TIM4_Init)
    MX_TIM6_Init                             0x08004a05   Thumb Code    66  tim.o(i.MX_TIM6_Init)
    MX_TIM8_Init                             0x08004a51   Thumb Code   224  tim.o(i.MX_TIM8_Init)
    MX_UART4_Init                            0x08004b39   Thumb Code    46  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x08004b71   Thumb Code    46  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08004ba9   Thumb Code    46  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004be1   Thumb Code    46  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004c19   Thumb Code    46  usart.o(i.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08004c51   Thumb Code    46  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08004c89   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004c8d   Thumb Code     4  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08004c91   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_DrawPoint                           0x08004cc1   Thumb Code   114  oled.o(i.OLED_DrawPoint)
    OLED_Init                                0x08004d39   Thumb Code   222  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08004e17   Thumb Code    22  oled.o(i.OLED_Pow)
    OLED_Refresh                             0x08004e2d   Thumb Code   100  oled.o(i.OLED_Refresh)
    OLED_ShowChar                            0x08004e95   Thumb Code   300  oled.o(i.OLED_ShowChar)
    OLED_ShowNum                             0x08004fd1   Thumb Code   140  oled.o(i.OLED_ShowNum)
    OLED_WR_Byte                             0x0800505d   Thumb Code    56  oled.o(i.OLED_WR_Byte)
    PendSV_Handler                           0x08005189   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SEG_GPIO_Init                            0x0800518d   Thumb Code   128  main.o(i.SEG_GPIO_Init)
    SEG_Refresh                              0x08005219   Thumb Code   152  main.o(i.SEG_Refresh)
    SEG_SetChar                              0x080052c1   Thumb Code    72  main.o(i.SEG_SetChar)
    SEG_ShowNumber                           0x08005311   Thumb Code   148  main.o(i.SEG_ShowNumber)
    SPI_LCD_Init                             0x080053a5   Thumb Code   464  lcd_spi_130.o(i.SPI_LCD_Init)
    SVC_Handler                              0x08005581   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Send_Byte                                0x08005585   Thumb Code    82  oled.o(i.Send_Byte)
    SysTick_Handler                          0x080055dd   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080055e5   Thumb Code   174  main.o(i.SystemClock_Config)
    SystemInit                               0x0800569d   Thumb Code    14  system_stm32f4xx.o(i.SystemInit)
    TFLUNA                                   0x080056b1   Thumb Code   174  exit_usart.o(i.TFLUNA)
    TIM6_DAC_IRQHandler                      0x0800577d   Thumb Code    10  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM_Base_SetConfig                       0x0800578d   Thumb Code   178  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x0800586d   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x0800588f   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08005929   Thumb Code   114  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_DMA                   0x08005fd5   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x080060ad   Thumb Code    64  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x080061d9   Thumb Code    10  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080061e9   Thumb Code    10  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x080061f9   Thumb Code    94  stm32f4xx_it.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x0800625d   Thumb Code    10  stm32f4xx_it.o(i.USART6_IRQHandler)
    UsageFault_Handler                       0x0800626d   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x08006271   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08006271   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08006271   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08006271   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08006271   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x080062c9   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080062d7   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080062d9   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    atk_ms901m_uart_rx_fifo_write            0x08006b75   Thumb Code    62  atk_ms901m_uart.o(i.atk_ms901m_uart_rx_fifo_write)
    fputc                                    0x08006bb9   Thumb Code    22  usart.o(i.fputc)
    ips200_showimage                         0x08006bd5   Thumb Code    82  lcd_spi_130.o(i.ips200_showimage)
    main                                     0x08006c29   Thumb Code   614  main.o(i.main)
    gImage_pic                               0x08006f48   Data       86400  main.o(.constdata)
    SEG_Table                                0x0801c0c8   Data          16  main.o(.constdata)
    AHBPrescTable                            0x0801c0d8   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0801c0e8   Data           8  system_stm32f4xx.o(.constdata)
    asc2_0806                                0x0801c0f0   Data         552  oled.o(.constdata)
    asc2_1206                                0x0801c318   Data        1140  oled.o(.constdata)
    asc2_1608                                0x0801c78c   Data        1520  oled.o(.constdata)
    asc2_2412                                0x0801cd7c   Data        3420  oled.o(.constdata)
    Hzk1                                     0x0801dad8   Data         352  oled.o(.constdata)
    Hzk2                                     0x0801dc38   Data          72  oled.o(.constdata)
    Hzk3                                     0x0801dc80   Data         128  oled.o(.constdata)
    Hzk4                                     0x0801dd00   Data         512  oled.o(.constdata)
    Chinese_1212                             0x0801df00   Data         480  lcd_fonts.o(.constdata)
    Chinese_1616                             0x0801e0e0   Data         640  lcd_fonts.o(.constdata)
    Chinese_2020                             0x0801e360   Data        1200  lcd_fonts.o(.constdata)
    Chinese_2424                             0x0801e810   Data        4680  lcd_fonts.o(.constdata)
    Chinese_3232                             0x0801fa58   Data        2560  lcd_fonts.o(.constdata)
    ASCII_3216_Table                         0x08020458   Data        6080  lcd_fonts.o(.constdata)
    ASCII_2412_Table                         0x08021c18   Data        4560  lcd_fonts.o(.constdata)
    ASCII_2010_Table                         0x08022de8   Data        3800  lcd_fonts.o(.constdata)
    ASCII_1608_Table                         0x08023cc0   Data        1520  lcd_fonts.o(.constdata)
    ASCII_1206_Table                         0x080242b0   Data        1140  lcd_fonts.o(.constdata)
    Region$$Table$$Base                      0x08024730   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08024750   Number         0  anon$$obj.o(Region$$Table)
    distence                                 0x20000000   Data           4  main.o(.data)
    angle                                    0x20000004   Data           4  main.o(.data)
    CX                                       0x20000008   Data           4  main.o(.data)
    CY                                       0x2000000c   Data           4  main.o(.data)
    var_id                                   0x20000010   Data           4  main.o(.data)
    value                                    0x20000014   Data           4  main.o(.data)
    read_value                               0x20000018   Data           4  main.o(.data)
    usart1_rx                                0x2000001c   Data           1  main.o(.data)
    usart2_rx                                0x2000001d   Data           1  main.o(.data)
    usart3_rx                                0x2000001e   Data           1  main.o(.data)
    usart4_rx                                0x2000001f   Data           1  main.o(.data)
    usart5_rx                                0x20000020   Data           1  main.o(.data)
    usart6_rx                                0x20000021   Data           1  main.o(.data)
    usart1_data_len                          0x20000022   Data           1  main.o(.data)
    test_1                                   0x20000024   Data           4  main.o(.data)
    test_2                                   0x20000028   Data           4  main.o(.data)
    a                                        0x2000002c   Data           4  main.o(.data)
    temp_1                                   0x20000030   Data           4  main.o(.data)
    TIM2CH1_CAP_STA                          0x20000034   Data           1  main.o(.data)
    TIM2CH1_CAP_VAL                          0x20000036   Data           2  main.o(.data)
    Display_Buffer                           0x20000038   Data           4  main.o(.data)
    boot                                     0x2000003c   Data           4  main.o(.data)
    SEG_Pins                                 0x20000040   Data          16  main.o(.data)
    uwTick                                   0x20000054   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000058   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x2000005c   Data           1  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000060   Data           4  system_stm32f4xx.o(.data)
    dist                                     0x20000064   Data           4  exit_usart.o(.data)
    check                                    0x20000068   Data           4  exit_usart.o(.data)
    count                                    0x2000006c   Data           4  exit_usart.o(.data)
    strength                                 0x20000070   Data           4  exit_usart.o(.data)
    Cx                                       0x20000074   Data           1  exit_usart.o(.data)
    Cy                                       0x20000075   Data           1  exit_usart.o(.data)
    CH_Font12                                0x2000007c   Data          12  lcd_fonts.o(.data)
    CH_Font16                                0x20000088   Data          12  lcd_fonts.o(.data)
    CH_Font20                                0x20000094   Data          12  lcd_fonts.o(.data)
    CH_Font24                                0x200000a0   Data          12  lcd_fonts.o(.data)
    CH_Font32                                0x200000ac   Data          12  lcd_fonts.o(.data)
    ASCII_Font32                             0x200000b8   Data          12  lcd_fonts.o(.data)
    ASCII_Font24                             0x200000c4   Data          12  lcd_fonts.o(.data)
    ASCII_Font20                             0x200000d0   Data          12  lcd_fonts.o(.data)
    ASCII_Font16                             0x200000dc   Data          12  lcd_fonts.o(.data)
    ASCII_Font12                             0x200000e8   Data          12  lcd_fonts.o(.data)
    __stdout                                 0x200000fc   Data           4  stdout.o(.data)
    usart5_rx_buf                            0x20000100   Data          50  main.o(.bss)
    count_temp_2_5                           0x20000138   Data          24  main.o(.bss)
    hadc1                                    0x20000150   Data          72  adc.o(.bss)
    hadc2                                    0x20000198   Data          72  adc.o(.bss)
    hi2c1                                    0x200001e0   Data          84  i2c.o(.bss)
    hspi1                                    0x20000234   Data          88  spi.o(.bss)
    htim1                                    0x2000028c   Data          72  tim.o(.bss)
    htim2                                    0x200002d4   Data          72  tim.o(.bss)
    htim3                                    0x2000031c   Data          72  tim.o(.bss)
    htim4                                    0x20000364   Data          72  tim.o(.bss)
    htim6                                    0x200003ac   Data          72  tim.o(.bss)
    htim8                                    0x200003f4   Data          72  tim.o(.bss)
    huart4                                   0x2000043c   Data          72  usart.o(.bss)
    huart5                                   0x20000484   Data          72  usart.o(.bss)
    huart1                                   0x200004cc   Data          72  usart.o(.bss)
    huart2                                   0x20000514   Data          72  usart.o(.bss)
    huart3                                   0x2000055c   Data          72  usart.o(.bss)
    huart6                                   0x200005a4   Data          72  usart.o(.bss)
    pFlash                                   0x200005ec   Data          32  stm32f4xx_hal_flash.o(.bss)
    OLED_GRAM                                0x2000060c   Data        1152  oled.o(.bss)
    uart                                     0x20000a8c   Data           9  exit_usart.o(.bss)
    hspi3                                    0x20000aac   Data          88  lcd_spi_130.o(.bss)
    LCD_Buff                                 0x20000b04   Data        2048  lcd_spi_130.o(.bss)
    LCD                                      0x20001304   Data          16  lcd_spi_130.o(.bss)
    __initial_sp                             0x200017e8   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00024850, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00024750, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         6161  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         6490    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         6493    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         6495    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         6497    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         6498    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         6505    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         6500    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         6502    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         6491    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         6164    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000024   Code   RO         6168    .text               mc_w.l(memseta.o)
    0x0800024a   0x0800024a   0x000000e4   Code   RO         6433    .text               mf_w.l(dmul.o)
    0x0800032e   0x0800032e   0x0000002c   Code   RO         6507    .text               mc_w.l(uidiv.o)
    0x0800035a   0x0800035a   0x0000001e   Code   RO         6509    .text               mc_w.l(llshl.o)
    0x08000378   0x08000378   0x00000020   Code   RO         6511    .text               mc_w.l(llushr.o)
    0x08000398   0x08000398   0x00000000   Code   RO         6520    .text               mc_w.l(iusefp.o)
    0x08000398   0x08000398   0x000000ba   Code   RO         6523    .text               mf_w.l(depilogue.o)
    0x08000452   0x08000452   0x0000014e   Code   RO         6525    .text               mf_w.l(dadd.o)
    0x080005a0   0x080005a0   0x000000de   Code   RO         6527    .text               mf_w.l(ddiv.o)
    0x0800067e   0x0800067e   0x00000030   Code   RO         6531    .text               mf_w.l(dfixul.o)
    0x080006ae   0x080006ae   0x00000002   PAD
    0x080006b0   0x080006b0   0x00000030   Code   RO         6535    .text               mf_w.l(cdrcmple.o)
    0x080006e0   0x080006e0   0x00000024   Code   RO         6537    .text               mc_w.l(init.o)
    0x08000704   0x08000704   0x00000024   Code   RO         6539    .text               mc_w.l(llsshr.o)
    0x08000728   0x08000728   0x00000160   Code   RO          775    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08000888   0x08000888   0x00000004   Code   RO          636    i.BusFault_Handler  stm32f4xx_it.o
    0x0800088c   0x0800088c   0x0000002c   Code   RO         1556    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080008b8   0x080008b8   0x00000002   Code   RO          637    i.DebugMon_Handler  stm32f4xx_it.o
    0x080008ba   0x080008ba   0x00000002   PAD
    0x080008bc   0x080008bc   0x000000ac   Code   RO         6022    i.EE_Init           eeprom_emul.o
    0x08000968   0x08000968   0x00000048   Code   RO         6023    i.EE_Read           eeprom_emul.o
    0x080009b0   0x080009b0   0x0000007c   Code   RO         6024    i.EE_Write          eeprom_emul.o
    0x08000a2c   0x08000a2c   0x0000000c   Code   RO          638    i.EXTI15_10_IRQHandler  stm32f4xx_it.o
    0x08000a38   0x08000a38   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x08000a3e   0x08000a3e   0x00000002   PAD
    0x08000a40   0x08000a40   0x0000005c   Code   RO         1324    i.FLASH_Erase_Sector  stm32f4xx_hal_flash_ex.o
    0x08000a9c   0x08000a9c   0x00000078   Code   RO         1325    i.FLASH_FlushCaches  stm32f4xx_hal_flash_ex.o
    0x08000b14   0x08000b14   0x00000030   Code   RO         1326    i.FLASH_MassErase   stm32f4xx_hal_flash_ex.o
    0x08000b44   0x08000b44   0x00000024   Code   RO         1206    i.FLASH_Program_Byte  stm32f4xx_hal_flash.o
    0x08000b68   0x08000b68   0x00000030   Code   RO         1207    i.FLASH_Program_DoubleWord  stm32f4xx_hal_flash.o
    0x08000b98   0x08000b98   0x00000028   Code   RO         1208    i.FLASH_Program_HalfWord  stm32f4xx_hal_flash.o
    0x08000bc0   0x08000bc0   0x00000028   Code   RO         1209    i.FLASH_Program_Word  stm32f4xx_hal_flash.o
    0x08000be8   0x08000be8   0x00000098   Code   RO         1210    i.FLASH_SetErrorCode  stm32f4xx_hal_flash.o
    0x08000c80   0x08000c80   0x00000064   Code   RO         1211    i.FLASH_WaitForLastOperation  stm32f4xx_hal_flash.o
    0x08000ce4   0x08000ce4   0x00000060   Code   RO         6025    i.FindActivePage    eeprom_emul.o
    0x08000d44   0x08000d44   0x0000001e   Code   RO         6088    i.FindEmptySlot     flash.o
    0x08000d62   0x08000d62   0x00000024   Code   RO         6026    i.FindFreeRecordAddr  eeprom_emul.o
    0x08000d86   0x08000d86   0x00000038   Code   RO         6027    i.Flash_ErasePage   eeprom_emul.o
    0x08000dbe   0x08000dbe   0x00000002   PAD
    0x08000dc0   0x08000dc0   0x0000002c   Code   RO         6089    i.Flash_Init        flash.o
    0x08000dec   0x08000dec   0x00000020   Code   RO         6028    i.Flash_ProgramWord  eeprom_emul.o
    0x08000e0c   0x08000e0c   0x00000038   Code   RO         6090    i.Flash_Read        flash.o
    0x08000e44   0x08000e44   0x00000038   Code   RO         6091    i.Flash_Write       flash.o
    0x08000e7c   0x08000e7c   0x0000003c   Code   RO         6092    i.Flash_WriteRecord  flash.o
    0x08000eb8   0x08000eb8   0x000000a0   Code   RO         6029    i.GetSector         eeprom_emul.o
    0x08000f58   0x08000f58   0x0000018c   Code   RO          777    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x080010e4   0x080010e4   0x00000006   Code   RO          783    i.HAL_ADC_GetState  stm32f4xx_hal_adc.o
    0x080010ea   0x080010ea   0x00000008   Code   RO          784    i.HAL_ADC_GetValue  stm32f4xx_hal_adc.o
    0x080010f2   0x080010f2   0x00000002   PAD
    0x080010f4   0x080010f4   0x00000068   Code   RO          786    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x0800115c   0x0800115c   0x000000e4   Code   RO          331    i.HAL_ADC_MspInit   adc.o
    0x08001240   0x08001240   0x000000ce   Code   RO          790    i.HAL_ADC_PollForConversion  stm32f4xx_hal_adc.o
    0x0800130e   0x0800130e   0x00000002   PAD
    0x08001310   0x08001310   0x0000014c   Code   RO          792    i.HAL_ADC_Start     stm32f4xx_hal_adc.o
    0x0800145c   0x0800145c   0x000000ac   Code   RO         1557    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08001508   0x08001508   0x00000028   Code   RO         1558    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08001530   0x08001530   0x00000092   Code   RO         1567    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x080015c2   0x080015c2   0x00000002   PAD
    0x080015c4   0x080015c4   0x00000028   Code   RO         1999    i.HAL_Delay         stm32f4xx_hal.o
    0x080015ec   0x080015ec   0x000000ac   Code   RO         1336    i.HAL_FLASHEx_Erase  stm32f4xx_hal_flash_ex.o
    0x08001698   0x08001698   0x00000014   Code   RO         1215    i.HAL_FLASH_Lock    stm32f4xx_hal_flash.o
    0x080016ac   0x080016ac   0x00000090   Code   RO         1220    i.HAL_FLASH_Program  stm32f4xx_hal_flash.o
    0x0800173c   0x0800173c   0x00000034   Code   RO         1222    i.HAL_FLASH_Unlock  stm32f4xx_hal_flash.o
    0x08001770   0x08001770   0x00000084   Code   RO           14    i.HAL_GPIO_EXTI_Callback  main.o
    0x080017f4   0x080017f4   0x0000001c   Code   RO         1449    i.HAL_GPIO_EXTI_IRQHandler  stm32f4xx_hal_gpio.o
    0x08001810   0x08001810   0x000001f4   Code   RO         1450    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001a04   0x08001a04   0x0000000c   Code   RO         1454    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001a10   0x08001a10   0x0000000c   Code   RO         2005    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001a1c   0x08001a1c   0x000001d0   Code   RO         2261    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x08001bec   0x08001bec   0x00000088   Code   RO          379    i.HAL_I2C_MspInit   i2c.o
    0x08001c74   0x08001c74   0x00000018   Code   RO         2011    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001c8c   0x08001c8c   0x0000003c   Code   RO         2012    i.HAL_Init          stm32f4xx_hal.o
    0x08001cc8   0x08001cc8   0x0000004c   Code   RO         2013    i.HAL_InitTick      stm32f4xx_hal.o
    0x08001d14   0x08001d14   0x00000048   Code   RO          748    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08001d5c   0x08001d5c   0x00000028   Code   RO         1842    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001d84   0x08001d84   0x0000007c   Code   RO         1848    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001e00   0x08001e00   0x00000028   Code   RO         1849    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001e28   0x08001e28   0x00000184   Code   RO         1051    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001fac   0x08001fac   0x0000000c   Code   RO         1056    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08001fb8   0x08001fb8   0x00000020   Code   RO         1058    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001fd8   0x08001fd8   0x00000020   Code   RO         1059    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001ff8   0x08001ff8   0x000000b0   Code   RO         1060    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080020a8   0x080020a8   0x0000049c   Code   RO         1063    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002544   0x08002544   0x0000016c   Code   RO         5513    i.HAL_SPI3_MspInit  lcd_spi_130.o
    0x080026b0   0x080026b0   0x000000c8   Code   RO         2756    i.HAL_SPI_Init      stm32f4xx_hal_spi.o
    0x08002778   0x08002778   0x00000088   Code   RO          421    i.HAL_SPI_MspInit   spi.o
    0x08002800   0x08002800   0x00000034   Code   RO         1853    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08002834   0x08002834   0x00000002   Code   RO         3784    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08002836   0x08002836   0x00000002   Code   RO         3785    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08002838   0x08002838   0x00000074   Code   RO         3787    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x080028ac   0x080028ac   0x000000b4   Code   RO         3803    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08002960   0x08002960   0x00000066   Code   RO         3069    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x080029c6   0x080029c6   0x00000002   PAD
    0x080029c8   0x080029c8   0x000000d0   Code   RO          463    i.HAL_TIM_Base_MspInit  tim.o
    0x08002a98   0x08002a98   0x000000a8   Code   RO         3074    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08002b40   0x08002b40   0x0000010c   Code   RO         3078    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08002c4c   0x08002c4c   0x000000c8   Code   RO         3090    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08002d14   0x08002d14   0x000000f4   Code   RO          465    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08002e08   0x08002e08   0x000000cc   Code   RO         3093    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x08002ed4   0x08002ed4   0x000000e4   Code   RO           15    i.HAL_TIM_IC_CaptureCallback  main.o
    0x08002fb8   0x08002fb8   0x0000016c   Code   RO         3117    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08003124   0x08003124   0x000000b0   Code   RO          466    i.HAL_TIM_MspPostInit  tim.o
    0x080031d4   0x080031d4   0x00000002   Code   RO         3120    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x080031d6   0x080031d6   0x00000104   Code   RO         3141    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x080032da   0x080032da   0x00000066   Code   RO         3144    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003340   0x08003340   0x00000002   Code   RO         3146    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08003342   0x08003342   0x00000002   Code   RO         3147    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08003344   0x08003344   0x0000010c   Code   RO         3149    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08003450   0x08003450   0x00000014   Code   RO           16    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x08003464   0x08003464   0x00000032   Code   RO         3157    i.HAL_TIM_ReadCapturedValue  stm32f4xx_hal_tim.o
    0x08003496   0x08003496   0x00000002   Code   RO         3160    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08003498   0x08003498   0x00000070   Code   RO         4061    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08003508   0x08003508   0x00000050   Code   RO           17    i.HAL_UARTEx_RxEventCallback  main.o
    0x08003558   0x08003558   0x00000002   Code   RO         4077    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800355a   0x0800355a   0x00000002   PAD
    0x0800355c   0x0800355c   0x00000308   Code   RO         4080    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08003864   0x08003864   0x00000076   Code   RO         4081    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x080038da   0x080038da   0x00000002   PAD
    0x080038dc   0x080038dc   0x00000354   Code   RO          553    i.HAL_UART_MspInit  usart.o
    0x08003c30   0x08003c30   0x000000c2   Code   RO         4084    i.HAL_UART_Receive  stm32f4xx_hal_uart.o
    0x08003cf2   0x08003cf2   0x0000002c   Code   RO         4086    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08003d1e   0x08003d1e   0x00000002   PAD
    0x08003d20   0x08003d20   0x0000009c   Code   RO           18    i.HAL_UART_RxCpltCallback  main.o
    0x08003dbc   0x08003dbc   0x00000002   Code   RO         4088    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08003dbe   0x08003dbe   0x000000be   Code   RO         4089    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08003e7c   0x08003e7c   0x00000002   Code   RO         4092    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003e7e   0x08003e7e   0x00000004   Code   RO          639    i.HardFault_Handler  stm32f4xx_it.o
    0x08003e82   0x08003e82   0x00000002   PAD
    0x08003e84   0x08003e84   0x00000040   Code   RO         5272    i.I2C_Start         oled.o
    0x08003ec4   0x08003ec4   0x0000002c   Code   RO         5273    i.I2C_Stop          oled.o
    0x08003ef0   0x08003ef0   0x00000034   Code   RO         5274    i.I2C_WaitAck       oled.o
    0x08003f24   0x08003f24   0x00000010   Code   RO         5275    i.IIC_delay         oled.o
    0x08003f34   0x08003f34   0x00000098   Code   RO         5444    i.K210              exit_usart.o
    0x08003fcc   0x08003fcc   0x000000dc   Code   RO         5514    i.LCD_Clear         lcd_spi_130.o
    0x080040a8   0x080040a8   0x00000080   Code   RO         5532    i.LCD_SetAddress    lcd_spi_130.o
    0x08004128   0x08004128   0x0000000c   Code   RO         5533    i.LCD_SetAsciiFont  lcd_spi_130.o
    0x08004134   0x08004134   0x00000028   Code   RO         5534    i.LCD_SetBackColor  lcd_spi_130.o
    0x0800415c   0x0800415c   0x00000028   Code   RO         5535    i.LCD_SetColor      lcd_spi_130.o
    0x08004184   0x08004184   0x000000b8   Code   RO         5536    i.LCD_SetDirection  lcd_spi_130.o
    0x0800423c   0x0800423c   0x0000000c   Code   RO         5538    i.LCD_ShowNumMode   lcd_spi_130.o
    0x08004248   0x08004248   0x000000b8   Code   RO         5539    i.LCD_WriteBuff     lcd_spi_130.o
    0x08004300   0x08004300   0x0000005c   Code   RO         5540    i.LCD_WriteCommand  lcd_spi_130.o
    0x0800435c   0x0800435c   0x00000034   Code   RO         5541    i.LCD_WriteData_16bit  lcd_spi_130.o
    0x08004390   0x08004390   0x0000001c   Code   RO         5542    i.LCD_WriteData_8bit  lcd_spi_130.o
    0x080043ac   0x080043ac   0x00000084   Code   RO         5445    i.MKF_K210          exit_usart.o
    0x08004430   0x08004430   0x00000070   Code   RO          332    i.MX_ADC1_Init      adc.o
    0x080044a0   0x080044a0   0x00000070   Code   RO          333    i.MX_ADC2_Init      adc.o
    0x08004510   0x08004510   0x000001cc   Code   RO          306    i.MX_GPIO_Init      gpio.o
    0x080046dc   0x080046dc   0x0000003c   Code   RO          380    i.MX_I2C1_Init      i2c.o
    0x08004718   0x08004718   0x00000044   Code   RO          422    i.MX_SPI1_Init      spi.o
    0x0800475c   0x0800475c   0x00000060   Code   RO         5543    i.MX_SPI3_Init      lcd_spi_130.o
    0x080047bc   0x080047bc   0x000000e8   Code   RO          467    i.MX_TIM1_Init      tim.o
    0x080048a4   0x080048a4   0x00000068   Code   RO          468    i.MX_TIM2_Init      tim.o
    0x0800490c   0x0800490c   0x0000007c   Code   RO          469    i.MX_TIM3_Init      tim.o
    0x08004988   0x08004988   0x0000007c   Code   RO          470    i.MX_TIM4_Init      tim.o
    0x08004a04   0x08004a04   0x0000004c   Code   RO          471    i.MX_TIM6_Init      tim.o
    0x08004a50   0x08004a50   0x000000e8   Code   RO          472    i.MX_TIM8_Init      tim.o
    0x08004b38   0x08004b38   0x00000038   Code   RO          554    i.MX_UART4_Init     usart.o
    0x08004b70   0x08004b70   0x00000038   Code   RO          555    i.MX_UART5_Init     usart.o
    0x08004ba8   0x08004ba8   0x00000038   Code   RO          556    i.MX_USART1_UART_Init  usart.o
    0x08004be0   0x08004be0   0x00000038   Code   RO          557    i.MX_USART2_UART_Init  usart.o
    0x08004c18   0x08004c18   0x00000038   Code   RO          558    i.MX_USART3_UART_Init  usart.o
    0x08004c50   0x08004c50   0x00000038   Code   RO          559    i.MX_USART6_UART_Init  usart.o
    0x08004c88   0x08004c88   0x00000004   Code   RO          640    i.MemManage_Handler  stm32f4xx_it.o
    0x08004c8c   0x08004c8c   0x00000004   Code   RO          641    i.NMI_Handler       stm32f4xx_it.o
    0x08004c90   0x08004c90   0x00000030   Code   RO         5276    i.OLED_Clear        oled.o
    0x08004cc0   0x08004cc0   0x00000078   Code   RO         5283    i.OLED_DrawPoint    oled.o
    0x08004d38   0x08004d38   0x000000de   Code   RO         5284    i.OLED_Init         oled.o
    0x08004e16   0x08004e16   0x00000016   Code   RO         5285    i.OLED_Pow          oled.o
    0x08004e2c   0x08004e2c   0x00000068   Code   RO         5286    i.OLED_Refresh      oled.o
    0x08004e94   0x08004e94   0x0000013c   Code   RO         5288    i.OLED_ShowChar     oled.o
    0x08004fd0   0x08004fd0   0x0000008c   Code   RO         5290    i.OLED_ShowNum      oled.o
    0x0800505c   0x0800505c   0x00000038   Code   RO         5293    i.OLED_WR_Byte      oled.o
    0x08005094   0x08005094   0x000000f4   Code   RO         6030    i.PageTransfer      eeprom_emul.o
    0x08005188   0x08005188   0x00000002   Code   RO          642    i.PendSV_Handler    stm32f4xx_it.o
    0x0800518a   0x0800518a   0x00000002   PAD
    0x0800518c   0x0800518c   0x0000008c   Code   RO           19    i.SEG_GPIO_Init     main.o
    0x08005218   0x08005218   0x000000a8   Code   RO           20    i.SEG_Refresh       main.o
    0x080052c0   0x080052c0   0x00000050   Code   RO           21    i.SEG_SetChar       main.o
    0x08005310   0x08005310   0x00000094   Code   RO           22    i.SEG_ShowNumber    main.o
    0x080053a4   0x080053a4   0x000001dc   Code   RO         5544    i.SPI_LCD_Init      lcd_spi_130.o
    0x08005580   0x08005580   0x00000002   Code   RO          643    i.SVC_Handler       stm32f4xx_it.o
    0x08005582   0x08005582   0x00000002   PAD
    0x08005584   0x08005584   0x00000058   Code   RO         5294    i.Send_Byte         oled.o
    0x080055dc   0x080055dc   0x00000008   Code   RO          644    i.SysTick_Handler   stm32f4xx_it.o
    0x080055e4   0x080055e4   0x000000b8   Code   RO           24    i.SystemClock_Config  main.o
    0x0800569c   0x0800569c   0x00000014   Code   RO         4425    i.SystemInit        system_stm32f4xx.o
    0x080056b0   0x080056b0   0x000000cc   Code   RO         5446    i.TFLUNA            exit_usart.o
    0x0800577c   0x0800577c   0x00000010   Code   RO          645    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x0800578c   0x0800578c   0x000000e0   Code   RO         3162    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x0800586c   0x0800586c   0x00000022   Code   RO         3163    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x0800588e   0x0800588e   0x00000016   Code   RO         3173    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x080058a4   0x080058a4   0x00000012   Code   RO         3174    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x080058b6   0x080058b6   0x00000002   PAD
    0x080058b8   0x080058b8   0x00000070   Code   RO         3175    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08005928   0x08005928   0x0000007c   Code   RO         3176    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x080059a4   0x080059a4   0x00000078   Code   RO         3177    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08005a1c   0x08005a1c   0x00000054   Code   RO         3178    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08005a70   0x08005a70   0x00000026   Code   RO         3180    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005a96   0x08005a96   0x00000028   Code   RO         3182    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005abe   0x08005abe   0x00000012   Code   RO         4094    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08005ad0   0x08005ad0   0x00000050   Code   RO         4095    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08005b20   0x08005b20   0x000000b4   Code   RO         4096    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08005bd4   0x08005bd4   0x00000024   Code   RO         4098    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08005bf8   0x08005bf8   0x0000006c   Code   RO         4104    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005c64   0x08005c64   0x00000020   Code   RO         4105    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08005c84   0x08005c84   0x00000026   Code   RO         4106    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005caa   0x08005caa   0x000000fc   Code   RO         4107    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005da6   0x08005da6   0x00000002   PAD
    0x08005da8   0x08005da8   0x0000022c   Code   RO         4108    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08005fd4   0x08005fd4   0x000000d8   Code   RO         4109    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080060ac   0x080060ac   0x00000040   Code   RO         4110    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080060ec   0x080060ec   0x00000060   Code   RO         4111    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x0800614c   0x0800614c   0x0000008c   Code   RO         4112    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x080061d8   0x080061d8   0x00000010   Code   RO          646    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080061e8   0x080061e8   0x00000010   Code   RO          647    i.USART2_IRQHandler  stm32f4xx_it.o
    0x080061f8   0x080061f8   0x00000064   Code   RO          648    i.USART3_IRQHandler  stm32f4xx_it.o
    0x0800625c   0x0800625c   0x00000010   Code   RO          649    i.USART6_IRQHandler  stm32f4xx_it.o
    0x0800626c   0x0800626c   0x00000004   Code   RO          650    i.UsageFault_Handler  stm32f4xx_it.o
    0x08006270   0x08006270   0x00000020   Code   RO         6405    i.__0printf         mc_w.l(printfa.o)
    0x08006290   0x08006290   0x00000010   Code   RO         1855    i.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080062a0   0x080062a0   0x00000028   Code   RO         1856    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080062c8   0x080062c8   0x0000000e   Code   RO         6545    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080062d6   0x080062d6   0x00000002   Code   RO         6546    i.__scatterload_null  mc_w.l(handlers.o)
    0x080062d8   0x080062d8   0x0000000e   Code   RO         6547    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080062e6   0x080062e6   0x00000002   PAD
    0x080062e8   0x080062e8   0x00000184   Code   RO         6412    i._fp_digits        mc_w.l(printfa.o)
    0x0800646c   0x0800646c   0x000006b4   Code   RO         6413    i._printf_core      mc_w.l(printfa.o)
    0x08006b20   0x08006b20   0x00000024   Code   RO         6414    i._printf_post_padding  mc_w.l(printfa.o)
    0x08006b44   0x08006b44   0x0000002e   Code   RO         6415    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08006b72   0x08006b72   0x00000002   PAD
    0x08006b74   0x08006b74   0x00000044   Code   RO         5876    i.atk_ms901m_uart_rx_fifo_write  atk_ms901m_uart.o
    0x08006bb8   0x08006bb8   0x0000001c   Code   RO          561    i.fputc             usart.o
    0x08006bd4   0x08006bd4   0x00000052   Code   RO         5545    i.ips200_showimage  lcd_spi_130.o
    0x08006c26   0x08006c26   0x00000002   PAD
    0x08006c28   0x08006c28   0x00000320   Code   RO           25    i.main              main.o
    0x08006f48   0x08006f48   0x00015190   Data   RO           27    .constdata          main.o
    0x0801c0d8   0x0801c0d8   0x00000018   Data   RO         4426    .constdata          system_stm32f4xx.o
    0x0801c0f0   0x0801c0f0   0x00001e10   Data   RO         5296    .constdata          oled.o
    0x0801df00   0x0801df00   0x00006824   Data   RO         5485    .constdata          lcd_fonts.o
    0x08024724   0x08024724   0x0000000c   Data   RO         6093    .constdata          flash.o
    0x08024730   0x08024730   0x00000020   Data   RO         6543    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08024750, Size: 0x000017e8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08024750   0x00000051   Data   RW           28    .data               main.o
    0x20000051   0x080247a1   0x00000003   PAD
    0x20000054   0x080247a4   0x00000009   Data   RW         2019    .data               stm32f4xx_hal.o
    0x2000005d   0x080247ad   0x00000003   PAD
    0x20000060   0x080247b0   0x00000004   Data   RW         4427    .data               system_stm32f4xx.o
    0x20000064   0x080247b4   0x00000018   Data   RW         5449    .data               exit_usart.o
    0x2000007c   0x080247cc   0x00000078   Data   RW         5486    .data               lcd_fonts.o
    0x200000f4   0x08024844   0x00000008   Data   RW         5547    .data               lcd_spi_130.o
    0x200000fc   0x0802484c   0x00000004   Data   RW         6506    .data               mc_w.l(stdout.o)
    0x20000100        -       0x00000050   Zero   RW           26    .bss                main.o
    0x20000150        -       0x00000090   Zero   RW          334    .bss                adc.o
    0x200001e0        -       0x00000054   Zero   RW          381    .bss                i2c.o
    0x20000234        -       0x00000058   Zero   RW          423    .bss                spi.o
    0x2000028c        -       0x000001b0   Zero   RW          473    .bss                tim.o
    0x2000043c        -       0x000001b0   Zero   RW          562    .bss                usart.o
    0x200005ec        -       0x00000020   Zero   RW         1223    .bss                stm32f4xx_hal_flash.o
    0x2000060c        -       0x00000480   Zero   RW         5295    .bss                oled.o
    0x20000a8c        -       0x0000001d   Zero   RW         5447    .bss                exit_usart.o
    0x20000aa9   0x08024850   0x00000003   PAD
    0x20000aac        -       0x00000868   Zero   RW         5546    .bss                lcd_spi_130.o
    0x20001314        -       0x000000ce   Zero   RW         5878    .bss                atk_ms901m_uart.o
    0x200013e2   0x08024850   0x00000006   PAD
    0x200013e8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       452         40          0          0        144       2494   adc.o
        68          6          0          0        206       1320   atk_ms901m_uart.o
       992         74          0          0          0       7467   eeprom_emul.o
       488         92          0         24         29       2853   exit_usart.o
       246         20         12          0          0       4224   flash.o
       460         20          0          0          0       1211   gpio.o
       196         24          0          0         84       1713   i2c.o
         0          0      26660        120          0       1907   lcd_fonts.o
      2010        122          0          8       2152      10682   lcd_spi_130.o
      2142        368      86416         81         80     741472   main.o
      1292         50       7696          0       1152       9595   oled.o
       204         24          0          0         88       1757   spi.o
        36          8        392          0       1024        848   startup_stm32f407xx.o
       212         36          0          9          0       9501   stm32f4xx_hal.o
      1404         52          0          0          0       5845   stm32f4xx_hal_adc.o
       312         22          0          0          0      34423   stm32f4xx_hal_cortex.o
       402          0          0          0          0       3674   stm32f4xx_hal_dma.o
       632         64          0          0         32       6090   stm32f4xx_hal_flash.o
       432         28          0          0          0       3318   stm32f4xx_hal_flash_ex.o
       540         50          0          0          0       2799   stm32f4xx_hal_gpio.o
       464         18          0          0          0       2171   stm32f4xx_hal_i2c.o
        72          4          0          0          0        886   stm32f4xx_hal_msp.o
      1820         84          0          0          0       5724   stm32f4xx_hal_rcc.o
       200          0          0          0          0       1231   stm32f4xx_hal_spi.o
      2810        142          0          0          0      19066   stm32f4xx_hal_tim.o
       300         30          0          0          0       3421   stm32f4xx_hal_tim_ex.o
      3256         28          0          0          0      18616   stm32f4xx_hal_uart.o
       210         30          0          0          0       7991   stm32f4xx_it.o
        20          6         24          4          0       1171   system_stm32f4xx.o
      1520        108          0          0        432       7055   tim.o
      1216        116          0          0        432       5887   usart.o

    ----------------------------------------------------------------------
     24440       <USER>     <GROUP>        252       5864     926412   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          0          6          9          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2218         90          0          0          0        464   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3656        <USER>          <GROUP>          4          0       1672   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2584        106          0          4          0       1016   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3656        <USER>          <GROUP>          4          0       1672   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     28096       1772     121232        256       5864     908556   Grand Totals
     28096       1772     121232        256       5864     908556   ELF Image Totals
     28096       1772     121232        256          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)               149328 ( 145.83kB)
    Total RW  Size (RW Data + ZI Data)              6120 (   5.98kB)
    Total ROM Size (Code + RO Data + RW Data)     149584 ( 146.08kB)

==============================================================================


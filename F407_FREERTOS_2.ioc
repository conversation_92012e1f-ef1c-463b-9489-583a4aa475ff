#MicroXplorer Configuration settings - do not modify
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_12
ADC1.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC1.IPParameters=Rank-2\#ChannelRegularConversion,master,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,NbrOfConversionFlag,ClockPrescaler
ADC1.NbrOfConversionFlag=1
ADC1.Rank-2\#ChannelRegularConversion=1
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
ADC2.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_13
ADC2.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC2.IPParameters=Rank-3\#ChannelRegularConversion,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,NbrOfConversionFlag,ClockPrescaler
ADC2.NbrOfConversionFlag=1
ADC2.Rank-3\#ChannelRegularConversion=1
ADC2.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
CAD.formats=[{"id"\:0,"cad_product"\:"Altium Designer","cad_family"\:"Altium"},{"id"\:36,"cad_product"\:"DXF-3D","cad_family"\:"Autodesk"}]
CAD.pinconfig=Project naming
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407ZET6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=TIM4
Mcu.IP11=TIM6
Mcu.IP12=TIM8
Mcu.IP13=UART4
Mcu.IP14=UART5
Mcu.IP15=USART1
Mcu.IP16=USART2
Mcu.IP17=USART3
Mcu.IP18=USART6
Mcu.IP2=I2C1
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SPI1
Mcu.IP6=SYS
Mcu.IP7=TIM1
Mcu.IP8=TIM2
Mcu.IP9=TIM3
Mcu.IPNb=19
Mcu.Name=STM32F407Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC13-ANTI_TAMP
Mcu.Pin1=PH0-OSC_IN
Mcu.Pin10=PA7
Mcu.Pin11=PC4
Mcu.Pin12=PC5
Mcu.Pin13=PB10
Mcu.Pin14=PB11
Mcu.Pin15=PB12
Mcu.Pin16=PB13
Mcu.Pin17=PB14
Mcu.Pin18=PB15
Mcu.Pin19=PD8
Mcu.Pin2=PH1-OSC_OUT
Mcu.Pin20=PD9
Mcu.Pin21=PC6
Mcu.Pin22=PC7
Mcu.Pin23=PC8
Mcu.Pin24=PC9
Mcu.Pin25=PA8
Mcu.Pin26=PA9
Mcu.Pin27=PA10
Mcu.Pin28=PA11
Mcu.Pin29=PA12
Mcu.Pin3=PC2
Mcu.Pin30=PA13
Mcu.Pin31=PA14
Mcu.Pin32=PA15
Mcu.Pin33=PC10
Mcu.Pin34=PC11
Mcu.Pin35=PC12
Mcu.Pin36=PD2
Mcu.Pin37=PD3
Mcu.Pin38=PD4
Mcu.Pin39=PD5
Mcu.Pin4=PC3
Mcu.Pin40=PD6
Mcu.Pin41=PD7
Mcu.Pin42=PG9
Mcu.Pin43=PG14
Mcu.Pin44=PB6
Mcu.Pin45=PB7
Mcu.Pin46=PB8
Mcu.Pin47=PB9
Mcu.Pin48=VP_SYS_VS_Systick
Mcu.Pin49=VP_TIM1_VS_ClockSourceINT
Mcu.Pin5=PA2
Mcu.Pin50=VP_TIM2_VS_ClockSourceINT
Mcu.Pin51=VP_TIM6_VS_ClockSourceINT
Mcu.Pin52=VP_TIM8_VS_ClockSourceINT
Mcu.Pin6=PA3
Mcu.Pin7=PA4
Mcu.Pin8=PA5
Mcu.Pin9=PA6
Mcu.PinsNb=53
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407ZETx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI15_10_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM6_DAC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:8\:0\:true\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:7\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.Locked=true
PA11.Signal=S_TIM1_CH4
PA12.GPIOParameters=GPIO_PuPd,GPIO_Label
PA12.GPIO_Label=KEY_2
PA12.GPIO_PuPd=GPIO_PULLUP
PA12.Locked=true
PA12.Signal=GPIO_Input
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_PuPd,GPIO_ModeDefaultEXTI
PA15.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PA15.GPIO_PuPd=GPIO_PULLUP
PA15.Locked=true
PA15.Signal=GPXTI15
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.GPIOParameters=GPIO_PuPd,GPIO_Label
PA4.GPIO_Label=KEY_1
PA4.GPIO_PuPd=GPIO_PULLUP
PA4.Locked=true
PA4.Signal=GPIO_Input
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA8.Locked=true
PA8.Signal=S_TIM1_CH1
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Locked=true
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Locked=true
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.Locked=true
PB15.Signal=GPIO_Output
PB6.Locked=true
PB6.Signal=S_TIM4_CH1
PB7.Locked=true
PB7.Signal=S_TIM4_CH2
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC10.Locked=true
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC11.Locked=true
PC11.Mode=Asynchronous
PC11.Signal=UART4_RX
PC12.Locked=true
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC13-ANTI_TAMP.Locked=true
PC13-ANTI_TAMP.Signal=GPIO_Output
PC2.Locked=true
PC2.Signal=ADCx_IN12
PC3.Locked=true
PC3.Signal=ADCx_IN13
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.Locked=true
PC5.Signal=GPIO_Output
PC6.Locked=true
PC6.Signal=S_TIM3_CH1
PC7.Locked=true
PC7.Signal=S_TIM3_CH2
PC8.Locked=true
PC8.Signal=S_TIM8_CH3
PC9.Locked=true
PC9.Signal=S_TIM8_CH4
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD3.GPIOParameters=GPIO_PuPd,GPIO_Label
PD3.GPIO_Label=KEY_3
PD3.GPIO_PuPd=GPIO_PULLUP
PD3.Locked=true
PD3.Signal=GPIO_Input
PD4.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=LED_1
PD4.GPIO_PuPd=GPIO_PULLUP
PD4.Locked=true
PD4.PinState=GPIO_PIN_SET
PD4.Signal=GPIO_Output
PD5.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD5.GPIO_Label=LED_2
PD5.GPIO_PuPd=GPIO_PULLUP
PD5.Locked=true
PD5.PinState=GPIO_PIN_SET
PD5.Signal=GPIO_Output
PD6.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD6.GPIO_Label=LED_3
PD6.GPIO_PuPd=GPIO_PULLUP
PD6.Locked=true
PD6.PinState=GPIO_PIN_SET
PD6.Signal=GPIO_Output
PD7.GPIOParameters=GPIO_Label
PD7.GPIO_Label=CE_24L01
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.GPIOParameters=PinState,GPIO_Label
PD8.GPIO_Label=CSN_24L01
PD8.Locked=true
PD8.PinState=GPIO_PIN_SET
PD8.Signal=GPIO_Output
PD9.GPIOParameters=GPIO_Label
PD9.GPIO_Label=IRQ_24L01
PD9.Locked=true
PD9.Signal=GPIO_Input
PG14.Locked=true
PG14.Mode=Asynchronous
PG14.Signal=USART6_TX
PG9.Locked=true
PG9.Mode=Asynchronous
PG9.Signal=USART6_RX
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=F407_FREERTOS_2.ioc
ProjectManager.ProjectName=F407_FREERTOS_2
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USART1_UART_Init-USART1-false-HAL-true,4-MX_USART3_UART_Init-USART3-false-HAL-true,5-MX_USART6_UART_Init-USART6-false-HAL-true,6-MX_ADC1_Init-ADC1-false-HAL-true,7-MX_ADC2_Init-ADC2-false-HAL-true,8-MX_TIM1_Init-TIM1-false-HAL-true,9-MX_TIM4_Init-TIM4-false-HAL-true,10-MX_TIM8_Init-TIM8-false-HAL-true,11-MX_USART2_UART_Init-USART2-false-HAL-true,12-MX_TIM3_Init-TIM3-false-HAL-true,13-MX_SPI1_Init-SPI1-false-HAL-true,14-MX_I2C1_Init-I2C1-false-HAL-true,15-MX_TIM6_Init-TIM6-false-HAL-true,16-MX_UART4_Init-UART4-false-HAL-true,17-MX_UART5_Init-UART5-false-HAL-true,18-MX_TIM2_Init-TIM2-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQ=7
RCC.PLLQCLKFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN12.0=ADC1_IN12,IN12
SH.ADCx_IN12.ConfNb=1
SH.ADCx_IN13.0=ADC2_IN13,IN13
SH.ADCx_IN13.ConfNb=1
SH.GPXTI15.0=GPIO_EXTI15
SH.GPXTI15.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
SH.S_TIM8_CH3.0=TIM8_CH3,PWM Generation3 CH3
SH.S_TIM8_CH3.ConfNb=1
SH.S_TIM8_CH4.0=TIM8_CH4,PWM Generation4 CH4
SH.S_TIM8_CH4.ConfNb=1
SPI1.CalculateBaudRate=42.0 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation4 CH4,Period,Prescaler
TIM1.Period=7199
TIM1.Prescaler=9
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=EncoderMode
TIM6.IPParameters=Period,Prescaler
TIM6.Period=999
TIM6.Prescaler=1680-1
TIM8.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM8.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM8.IPParameters=Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4,Prescaler,Period
TIM8.Period=1999
TIM8.Prescaler=1680-1
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
VP_TIM8_VS_ClockSourceINT.Mode=Internal
VP_TIM8_VS_ClockSourceINT.Signal=TIM8_VS_ClockSourceINT
board=custom

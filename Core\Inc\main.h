/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */
void SHURUBUHUO(void);
void SEG_Refresh(void);
void SEG_GPIO_Init(void) ;
void SEG_SetChar(uint8_t pos, char ch);
void SEG_ShowNumber(uint16_t number);
/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define KEY_1_Pin GPIO_PIN_4
#define KEY_1_GPIO_Port GPIOA
#define CSN_24L01_Pin GPIO_PIN_8
#define CSN_24L01_GPIO_Port GPIOD
#define IRQ_24L01_Pin GPIO_PIN_9
#define IRQ_24L01_GPIO_Port GPIOD
#define KEY_2_Pin GPIO_PIN_12
#define KEY_2_GPIO_Port GPIOA
#define KEY_3_Pin GPIO_PIN_3
#define KEY_3_GPIO_Port GPIOD
#define LED_1_Pin GPIO_PIN_4
#define LED_1_GPIO_Port GPIOD
#define LED_2_Pin GPIO_PIN_5
#define LED_2_GPIO_Port GPIOD
#define LED_3_Pin GPIO_PIN_6
#define LED_3_GPIO_Port GPIOD
#define CE_24L01_Pin GPIO_PIN_7
#define CE_24L01_GPIO_Port GPIOD

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */

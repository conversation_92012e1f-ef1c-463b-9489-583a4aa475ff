/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "i2c.h"
#include "spi.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "my_mpu6050.h"
#include "NRF24L01.h"
#include "key.h"
#include "oled.h"
#include "lcd_spi_130.h"
#include "stdio.h"
#include "string.h"
#include "exit_usart.h"
#include	"lcd_image.h"
#include "pic.h"
//#include "delay.h"
#include "atk_ms901m_uart.h"
#include "atk_ms901m.h"
#include  "flash.h"
#include "eeprom_emul.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
int distence=0;
int angle=0;
int CX,CY;
		uint32_t var_id = 0x01; 	// 设置你的变量标识符
    uint32_t value = 15; 		 // 你要保存的数据
	// 读取数据
    uint32_t read_value = 0;
//全局变量
//uint8_t uart[9];
uint8_t usart5_rx_buf[50];
uint8_t usart1_rx;
uint8_t usart2_rx;
uint8_t usart3_rx;
uint8_t usart4_rx;
uint8_t usart5_rx;
uint8_t usart6_rx;


uint8_t usart1_data_len;
int test_1,test_2;
	int a=8;

//全局变量
long temp_1=0;
uint8_t TIM2CH1_CAP_STA = 0;
uint16_t TIM2CH1_CAP_VAL;
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
//数码管段码表
const uint8_t SEG_Table[] = {
    // 0~9
    0x3F, // 0: 0b00111111
    0x06, // 1
    0x5B, // 2
    0x4F, // 3
    0x66, // 4
    0x6D, // 5
    0x7D, // 6
    0x07, // 7
    0x7F, // 8
    0x6F, // 9
    // A~F
    0x77, // A
    0x7C, // b
    0x39, // C
    0x5E, // d
    0x79, // E
    0x71  // F
};

// 数码管显示缓存（4 位）
uint8_t Display_Buffer[4] = {0, 0, 0, 0};

// 位选引脚宏定义
#define DIG1_Pin GPIO_PIN_15
#define DIG2_Pin GPIO_PIN_14
#define DIG3_Pin GPIO_PIN_13
#define DIG4_Pin GPIO_PIN_11
#define DIG_Port GPIOF
#define VAR_BOOT_CNT  0x0001U

		uint32_t boot = 0;
// 段选引脚数组（A,B,C,D,E,F,G,DP）
uint16_t SEG_Pins[8] = {
    GPIO_PIN_0, // A
    GPIO_PIN_2, // B
    GPIO_PIN_4, // C
    GPIO_PIN_6, // D
    GPIO_PIN_7, // E
    GPIO_PIN_1, // F
    GPIO_PIN_3, // G
    GPIO_PIN_5  // DP
};


/* 在 main.c 文件中，可以放在 SEG_Refresh() 函数的上面 */

/**
  * @brief  在4位数码管上显示一个整数.
  * @param  number 要显示的数字 (范围 0 ~ 9999).
  * @retval None
  */
void SEG_ShowNumber(uint16_t number)
{
    uint8_t qian, bai, shi, ge;

    // 数据合法性检查，防止数字超过显示范围
    if (number > 9999) {
        // 如果数字太大，可以显示错误提示，例如 "EEEE"
        SEG_SetChar(1, 'E');
        SEG_SetChar(2, 'E');
        SEG_SetChar(3, 'E');
        SEG_SetChar(4, 'E');
        return;
    }

    // 分离各个位上的数字
    qian = number / 1000;
    bai  = (number / 100) % 10;
    shi  = (number / 10) % 10;
    ge   = number % 10;

    // 将数字转换成字符并设置到显示缓存中
    // 注意：数字 + '0' 即可得到对应的ASCII字符，例如 5 + '0' = '5'
    SEG_SetChar(1, bai + '0'); // 第1位显示千位
    SEG_SetChar(2, shi + '0');  // 第2位显示百位
    SEG_SetChar(3, ge + '0');  // 第3位显示十位
    //SEG_SetChar(4, ge + '0');  // 第4位显示个位
}

// 初始化数码管相关的GPIO
void SEG_GPIO_Init(void) {
    __HAL_RCC_GPIOF_CLK_ENABLE();

    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;

    // 初始化段选引脚
    GPIO_InitStruct.Pin = SEG_Pins[0] | SEG_Pins[1] | SEG_Pins[2] |
                          SEG_Pins[3] | SEG_Pins[4] | SEG_Pins[5] |
                          SEG_Pins[6] | SEG_Pins[7];
    HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);

    // 初始化位选引脚
    GPIO_InitStruct.Pin = DIG1_Pin | DIG2_Pin | DIG3_Pin | DIG4_Pin;
    HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);
}

// 设置要显示的字符（pos: 1~4）
void SEG_SetChar(uint8_t pos, char ch) {
    uint8_t code = 0x00;
    if (ch >= '0' && ch <= '9') {
        code = SEG_Table[ch - '0'];
    } else if (ch >= 'A' && ch <= 'F') {
        code = SEG_Table[ch - 'A' + 10];
    } else if (ch >= 'a' && ch <= 'f') {
        code = SEG_Table[ch - 'a' + 10];
    }
    if (pos >= 1 && pos <= 4) {
        Display_Buffer[pos - 1] = code;
    }
}

// 刷新显示（在定时器中断中调用）
void SEG_Refresh(void) {
    static uint8_t pos = 0;

    // 关闭所有位选（高电平关闭）
    HAL_GPIO_WritePin(DIG_Port, DIG1_Pin | DIG2_Pin | DIG3_Pin | DIG4_Pin, GPIO_PIN_SET);

    // 输出段码
    for (int i = 0; i < 8; i++) {
        HAL_GPIO_WritePin(GPIOF, SEG_Pins[i], (Display_Buffer[pos] >> i) & 0x01 ? GPIO_PIN_SET : GPIO_PIN_RESET);
    }

    // 打开当前位（低电平选中）
    switch (pos) {
        case 0: HAL_GPIO_WritePin(DIG_Port, DIG1_Pin, GPIO_PIN_RESET); break;
        case 1: HAL_GPIO_WritePin(DIG_Port, DIG2_Pin, GPIO_PIN_RESET); break;
        case 2: HAL_GPIO_WritePin(DIG_Port, DIG3_Pin, GPIO_PIN_RESET); break;
        case 3: HAL_GPIO_WritePin(DIG_Port, DIG4_Pin, GPIO_PIN_RESET); break;
    }

    pos = (pos + 1) % 4;
}


typedef struct 
{   
    uint8_t   flg_2_5; //0:未开始 1:下降沿 2:上升沿
    uint64_t  num_2_5;//计数值
    uint64_t  num_period_2_5;//溢出次数
}COUNT_TEMP_2_5;
COUNT_TEMP_2_5 count_temp_2_5={0};
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */
	//MPU6050相关变量
short aacx,aacy,aacz,gyrox,gyroy,gyroz,yaw,roll,pitch;
	//NRF24L01相关变量
	unsigned char flag_nrf24l01 = 0;
uint8_t buffer[4] = {0x00, 0x00, 0x00, 0x00};
unsigned char tmp_buf[32] = {0};
unsigned char tmp_buf2[4] = {0};
unsigned char temp;
float ADC_Value_1,ADC_Value_2;
//ADC相关变量
int ADC_1,ADC_2;
	uint8_t ret;
   uint8_t key;
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  MX_USART3_UART_Init();
  MX_USART6_UART_Init();
  MX_ADC1_Init();
  MX_ADC2_Init();
  MX_TIM1_Init();
  MX_TIM4_Init();
  MX_TIM8_Init();
  MX_USART2_UART_Init();
  MX_TIM3_Init();
  MX_SPI1_Init();
  MX_I2C1_Init();
  MX_TIM6_Init();
  MX_UART4_Init();
  MX_UART5_Init();
  MX_TIM2_Init();

//	EXTI_Init();
  /* USER CODE BEGIN 2 */
		SEG_GPIO_Init();
		    EE_Init();
		 Flash_Init();
    if (EE_Read(VAR_BOOT_CNT, &boot) != EE_OK) {
        boot = 0; /* 第一次 */
    }

    boot++;
    EE_Write(VAR_BOOT_CNT, boot);
		

    /* 初始化ATK-MS901M */
   // ret = atk_ms901m_init(115200);
   // if (ret != 0)
  //  {
  //      printf("ATK-MS901M init failed!\r\n");
  //      while (1)
   //     {
  //          HAL_GPIO_TogglePin(GPIOC,GPIO_PIN_13);
  //          HAL_Delay(500);
  //      }
  //  }
   // printf("ATK-MS901M init success!\r\n\n");

	OLED_Init();							//OLED初始化
	SPI_LCD_Init();						// SPI LCD初始化
	HAL_ADC_Start(&hadc1);   //启动ADC1
  HAL_ADC_Start(&hadc2);   //启动ADC2

	//编码器模式初始化
HAL_TIM_Encoder_Start(&htim3,TIM_CHANNEL_1);
HAL_TIM_Encoder_Start(&htim3,TIM_CHANNEL_2);
HAL_TIM_Encoder_Start(&htim4,TIM_CHANNEL_1);
HAL_TIM_Encoder_Start(&htim4,TIM_CHANNEL_2);
	//PWM输出初始化
HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_1);
HAL_TIM_PWM_Start(&htim1,TIM_CHANNEL_4);
HAL_TIM_PWM_Start(&htim8,TIM_CHANNEL_3);
HAL_TIM_PWM_Start(&htim8,TIM_CHANNEL_4);

//NRF24L01初始化
//	NRF24L01_Init();
//	printf("\r\ninit OK!\r\n");
//	temp = 4;
//	while(NRF24L01_Check()&&(temp--));
//	TX_Mode();	//设置为发送模式

//MPU6050初始化
//	while(mpu_dmp_init())
//{
//	//int i=mpu_dmp_init();
//	printf("error\r\n");
//	HAL_Delay(10);
//}
  
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
	
	
	
	//按键扫描
//	while(!Key_Scam())
//	{
//		uint8_t i;
//		i=Key_Scam();
//		switch(Key_Scam())
//		{
//		case 1: {HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_4); break;}
//    case 2: {HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_5); break;}
//    case 3: {HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_6); break;}
//    case 4: {HAL_GPIO_TogglePin(GPIOC,GPIO_PIN_13); break;}
//		case 0: {; break;}
//	  }
//	}
//	
//    // 设置要显示的内容
//    SEG_SetChar(1, a);
//    SEG_SetChar(2, '2');
//    SEG_SetChar(3, '1');
//    SEG_SetChar(4, '2');



//显示函数说明
//x,y :起始坐标
//num :要显示的数字
//len :数字的位数
//size:字体大小
//mode:0,反色显示;1,正常显示
  OLED_ShowNum(16,16,boot,3,12,1);
	//OLED_ShowNum(64,33,i,12,1);
	OLED_ShowChar(72,33,'5',12,1);
	OLED_ShowChar(80,33,'0',12,1);
	OLED_ShowChar(88,33,':',12,1);
  OLED_Refresh();//刷新OLED显示
	
	
	
	
	
	
	//开启串口接收中断
//	HAL_UART_Receive_IT(&huart1,(void*)&usart1_rx,1);
//	HAL_UART_Receive_IT(&huart2,(void*)&usart2_rx,1);
//	HAL_UART_Receive_IT(&huart3,(void*)&usart3_rx,1);
//	HAL_UART_Receive_IT(&huart4,(void*)&usart4_rx,1);//TFLUNA
//	HAL_UART_Receive_IT(&huart5,(void*)&usart5_rx,1);//自定义
//	HAL_UART_Receive_IT(&huart6,(void*)&usart6_rx,1);
//  HAL_UARTEx_ReceiveToIdle_DMA(&huart5,usart5_rx_buf,1024);

//开启定时器6基本中断
HAL_TIM_Base_Start_IT(&htim6);
	
	
//开启输入捕获
//  __HAL_TIM_ENABLE_IT(&htim2,TIM_IT_UPDATE); //使能TIM2更新中断
//	__HAL_TIM_SET_CAPTUREPOLARITY(&htim2, TIM_CHANNEL_1, TIM_INPUTCHANNELPOLARITY_FALLING); 
//	HAL_TIM_IC_Start_IT(&htim2, TIM_CHANNEL_1);    //开始输入捕获
//	
	
	//LCD_SetBackColor(LCD_BLACK); 			//设置背景色为黑色
	//LCD_SetColor( 0x000000F8);
	 
		
		
	ips200_showimage( 0, 0, 240, 180, gImage_pic) ;	   // 显示图片

	TIM8->CCR3=150;//84 217
	TIM8->CCR4=150;
  while (1)
  {
		
//	while(!Key_Scam())
//	{
//		uint8_t i;
//		i=Key_Scam();
//		switch(Key_Scam())
//		{
//		case 1: {     a=a*a;   OLED_ShowNum(1,1,a,3,12,1); break;}
//    case 2: {     a=a+1;   OLED_ShowNum(1,1,a,3,12,1);break;}
//    case 3: {     a=a-1;   OLED_ShowNum(1,1,a,3,12,1); break;}
//    case 4: {     a=a/2;   OLED_ShowNum(1,1,a,3,12,1); break;}
//		case 0: {  break;}
//	  }
//	}
//	
		
	 if (Flash_Read(var_id, &read_value) == 0) {
		 
		 printf(" read_value=%d   on\r\n",read_value);
        // 成功读取
    } else {
			
			 printf("read_value=%d   off\r\n",read_value);
        // 读取失败
    }
		
		OLED_ShowNum(0,84,read_value,3,12,1);
		OLED_Refresh();
		
//    SEG_SetChar(1, 'e');
//    SEG_SetChar(2, '2');
//    SEG_SetChar(3, '1');
//    SEG_SetChar(4, '0');
		
		
		
//		HAL_GPIO_TogglePin(GPIOD, LED_1_Pin|LED_2_Pin|LED_3_Pin);
//		HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
//		HAL_Delay(1000);
		
		
	//	Key_Scam();
		
		
//		switch(Key_Scam())
//		{
//		case 1: {HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_4); break;}
//    case 2: {HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_5); break;}
//    case 3: {HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_6); break;}
//    case 4: {HAL_GPIO_TogglePin(GPIOC,GPIO_PIN_13); break;}
//		case 0: { break;}
//	  }
//		atk_ms901m_attitude_data_t attitude_dat;           /* 姿态数据 */
//    atk_ms901m_gyro_data_t gyro_dat;                   /* 陀螺仪数据 */
//    atk_ms901m_accelerometer_data_t accelerometer_dat; /* 加速度计数据 */
//    atk_ms901m_magnetometer_data_t magnetometer_dat;   /* 磁力计数据 */
//    atk_ms901m_barometer_data_t barometer_dat;         /* 气压计数据 */
    
    /* 读取ATK-MS901M数据 */
//   atk_ms901m_get_attitude(&attitude_dat, 100);                            /* 获取姿态数据 */
//    atk_ms901m_get_gyro_accelerometer(&gyro_dat, &accelerometer_dat, 100);  /* 获取陀螺仪和加速度计数据 */
//    atk_ms901m_get_magnetometer(&magnetometer_dat, 100);                    /* 获取磁力计数据 */
//    atk_ms901m_get_barometer(&barometer_dat, 100);                          /* 获取气压计数据 */
    
   
    
    /* 打印数据 */
//    printf("Roll: %.02f° Pitch: %.02f° Yaw: %.02f°\r\n", attitude_dat.roll, attitude_dat.pitch, attitude_dat.yaw);
//    printf("Gx: %.02f°/s Gy: %.02f°/s Gz: %.02f°/s\r\n", gyro_dat.x, gyro_dat.y, gyro_dat.z);
//    printf("Ax: %.02fG Ay: %.02fG Az: %.02fG\r\n", accelerometer_dat.x, accelerometer_dat.y, accelerometer_dat.z);
//    printf("Mx: %d My: %d Mz: %d, Temp: %.02f°\r\n", magnetometer_dat.x, magnetometer_dat.y, magnetometer_dat.z, magnetometer_dat.temperature);
//    printf("Pres: %dPa Alt: %dcm Temp: %.02f°\r\n", barometer_dat.pressure, barometer_dat.altitude, barometer_dat.temperature);
//    printf("****************************************\r\n\r\n");
//   uint8_t  cmd[]={0x03,0xfc,(int)attitude_dat.roll,(int)attitude_dat.pitch,(int)attitude_dat.yaw,0xfc,0x03};//自定义协议 帧头0X03,0XFC 帧尾0xfC 0x03
//		for(int i=0;i<7;i++)//循环发送
//		HAL_UART_Transmit(&huart1,&cmd[i],1,0xfff);
//		TIM8->CCR3=150+attitude_dat.pitch;
//	  TIM8->CCR4=150+attitude_dat.yaw+90;
//		HAL_Delay(100);


//		if(count_temp_2_5.flg_2_5 == 2 )
//		{
//    //计算溢出时间
//    long ulTime = (uint32_t)count_temp_2_5 .num_period_2_5 * 65535 + count_temp_2_5 .num_2_5;
//    //计算PM2.5值
//  	PM2_5=ulTime/1000; 
//  	OLED_ShowNum(48,0,PM2_5,3,12);//在OLED上显示
//    printf ( "PM2.5低电平时间:%ld us\n",ulTime); 
//    count_temp_2_5.flg_2_5 = 0;            
//}
		
		
		//输入捕获测高电平时间
//		if(TIM2CH1_CAP_STA & 0X80){  //成功捕获了一次高电平
//  temp_1 = TIM2CH1_CAP_STA & 0X3F;
//  temp_1 *= 65535;    //溢出时间
//  temp_1 += TIM2CH1_CAP_VAL; //得到总的高电平时间
//	printf("High level duration:%ld us\r\n",temp_1*2);
//  TIM2CH1_CAP_STA = 0;  //开启下一次捕获
// }
//	
 
 //ADC转换
	printf("%d\r\n",test_1);
	HAL_ADC_Start(&hadc1);
  HAL_ADC_Start(&hadc2);
	HAL_ADC_PollForConversion(&hadc1, 50);
	if(HAL_IS_BIT_SET(HAL_ADC_GetState(&hadc1), HAL_ADC_STATE_REG_EOC))
	{
	ADC_Value_1 = HAL_ADC_GetValue(&hadc1);
	ADC_1=ADC_Value_1*999/4096;
	tmp_buf[0]=ADC_1;
	printf("PC4 True Voltage value : %d \r\n",ADC_1);
	
	HAL_Delay(10);
	}
	HAL_ADC_PollForConversion(&hadc2, 50);
	if(HAL_IS_BIT_SET(HAL_ADC_GetState(&hadc2), HAL_ADC_STATE_REG_EOC))
	{
	ADC_Value_2 = HAL_ADC_GetValue(&hadc2);
	ADC_2=ADC_Value_2*999/4096;
	tmp_buf[1]=ADC_2;
	printf("PC5 True Voltage value : %d \r\n",ADC_2);
	
	HAL_Delay(10);
	}
	a++;
	
 SEG_ShowNumber(a);
	
		HAL_Delay(1000);
	
	
//	while(NRF24L01_TxPacket(tmp_buf)!= 0x20);


//		按键发送数据
//		switch(Key_Scam())
//		{
//		case 1: {tmp_buf[0]=0X01;while(NRF24L01_TxPacket(tmp_buf)!= 0x20); break;}
//    case 2: {tmp_buf[0]=0X02;while(NRF24L01_TxPacket(tmp_buf)!= 0x20); break;}
//    case 3: {tmp_buf[0]=0X03;while(NRF24L01_TxPacket(tmp_buf)!= 0x20); break;}
//    case 4: {tmp_buf[0]=0X04;while(NRF24L01_TxPacket(tmp_buf)!= 0x20); break;}
//		case 0: {tmp_buf[0]=0X00;while(NRF24L01_TxPacket(tmp_buf)!= 0x20); break;}
//	  }
//		printf("000%d\r\n ",tmp_buf[0]);
		
		
		
		
		
		
//		HAL_Delay(10);
//		dmp_getdata();
//		//MPU6050_Read_Temp();
//		pitch= (short)My_Mpu6050.pitch;
//		yaw =(short)My_Mpu6050.yaw;
//		roll =(short)My_Mpu6050.roll;
//		aacx = My_Mpu6050.accel[0];//单位：m/s^2
//		aacy = My_Mpu6050.accel[1];
//		aacz = My_Mpu6050.accel[2]; 
//		gyrox = My_Mpu6050.gyro[0];//单位：rad/s
//		gyroy = My_Mpu6050.gyro[1];
//		gyroz = My_Mpu6050.gyro[2];
//		//printf("pitch:%.2f  gyroy%d \r\n",My_Mpu6050.pitch,My_Mpu6050.gyro[1]);
//		 uint8_t  cmd[]={0x03,0xfc,pitch,yaw,roll,0xfc,0x03};//自定义协议 帧头0X03,0XFC 帧尾0xfC 0x03
//		for(int i=0;i<7;i++)//循环发送
//		HAL_UART_Transmit(&huart6,&cmd[i],1,0xfff);

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 7;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/**
  * @brief  定时器周期溢出中断回调函数
  * @param  htim TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	
	
	if(htim==(&htim6)){
	SEG_Refresh(); // 调用数码管刷新函数
	}
}

//外部中断回调函数
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
 if(GPIO_Pin==GPIO_PIN_15)
 {
 
   a=a+1;   
	 OLED_ShowNum(1,1,a,3,12,1);
	 OLED_Refresh();
  value++;
	if(Flash_Write(var_id, value)==1)
{
printf("write_success\r\n");
}
printf("write_error\r\n");
	 
 }
}

void SHURUBUHUO(void) // 输入捕获处理函数（此函数未被调用）
{
	if((TIM2CH1_CAP_STA & 0X80) == 0){ //还未成功捕获
  if(TIM2CH1_CAP_STA & 0X40){  //已经捕获到下降沿了
   if((TIM2CH1_CAP_STA & 0X3F) == 0X3F){ //高电平时间太长，溢出了
    TIM2CH1_CAP_STA |= 0X80;   //标记成功捕获
    TIM2CH1_CAP_VAL = 0XFFFF;
   }
   else
    TIM2CH1_CAP_STA++;  //溢出次数加1
  } 
 }
}

// 定时器输入捕获回调函数
void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim){
 if(TIM2 == htim->Instance){
							if((TIM2CH1_CAP_STA & 0X80) == 0){ //还未成功捕获
        if(TIM2CH1_CAP_STA & 0X40){  //捕获到了一个下降沿
         TIM2CH1_CAP_STA |= 0X80; //标记成功捕获
         TIM2CH1_CAP_VAL = HAL_TIM_ReadCapturedValue(&htim2,TIM_CHANNEL_1); //获取当前的捕获值
         TIM_RESET_CAPTUREPOLARITY(&htim2,TIM_CHANNEL_1); //清除上次的捕获极性
         TIM_SET_CAPTUREPOLARITY(&htim2,TIM_CHANNEL_1,TIM_ICPOLARITY_FALLING); //设置为下降沿捕获
        }
        else{ //还未开始,第一次进入
         TIM2CH1_CAP_STA = 0;
         TIM2CH1_CAP_VAL = 0;
         TIM2CH1_CAP_STA |= 0X40; //标记已捕获到下降沿
         __HAL_TIM_DISABLE(&htim2); //关闭定时器
         __HAL_TIM_SET_COUNTER(&htim2,0); //计数器清零
         TIM_RESET_CAPTUREPOLARITY(&htim2,TIM_CHANNEL_1); //清除上次的捕获极性
         TIM_SET_CAPTUREPOLARITY(&htim2,TIM_CHANNEL_1,TIM_ICPOLARITY_RISING); //设置为上升沿捕获
         __HAL_TIM_ENABLE(&htim2); //使能定时器  
	
        } 
//            if ( count_temp_2_5.flg_2_5 == 0 )
//            {   
//                // 第一次进入，启动捕获
//                __HAL_TIM_SET_COUNTER(htim,0); 
//                //设置为上升沿捕获
//                __HAL_TIM_SET_CAPTUREPOLARITY(&htim2, TIM_CHANNEL_1, TIM_INPUTCHANNELPOLARITY_RISING);
//                count_temp_2_5.flg_2_5 = 1;    //标记已捕获下降沿
//                count_temp_2_5.num_period_2_5 = 0;    //溢出次数清零
//                count_temp_2_5.num_2_5 = 0; //计数值清零
//            }        
//            else
//            {
//                // 捕获到高电平时间
//                count_temp_2_5.num_2_5 = HAL_TIM_ReadCapturedValue(&htim2,TIM_CHANNEL_1);
//                //设置为下降沿捕获
//                __HAL_TIM_SET_CAPTUREPOLARITY(&htim2, TIM_CHANNEL_1, TIM_INPUTCHANNELPOLARITY_FALLING);
//                count_temp_2_5.flg_2_5 = 2;
//            }
						
        }
} 
}

//串口空闲接收中断回调函数
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	if(huart == &huart5)
	{
		//计算接收数据长度
		usart1_data_len = 1024 - huart->hdmarx->Instance->NDTR;
		//sscanf(usart5_rx_buf,"N%d\r\n",&test_1);
		//HAL_UART_Transmit(&huart1,usart5_rx_buf,usart1_data_len,100);
		printf("%d\r\n",test_1);
		//重置DMA传输计数器
		huart->hdmarx->Instance -> NDTR =  1024;
		//重新开启DMA空闲中断接收
		HAL_UARTEx_ReceiveToIdle_DMA(&huart5,usart5_rx_buf,1024);
	}
}


// 串口接收完成回调函数 (非DMA模式)
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	
	if(huart->Instance==USART2) // K210
	{
		uint8_t com_data;
		com_data=usart2_rx;
		CX=K210(com_data);
		HAL_UART_Receive_IT(&huart2,&usart2_rx,1);//重新使能串口接收中断
}
	if(huart->Instance == UART4)// 如果是串口4 (TFLUNA激光雷达)
	{
		uint8_t com_data;
		com_data=usart4_rx;
	distence=TFLUNA(com_data);
	HAL_UART_Receive_IT(&huart4,&usart4_rx,1);//重新使能串口接收中断	
	}
	
	if(huart->Instance==UART5) // 自定义协议
	{
		uint8_t com_data;
		com_data=usart5_rx;
    angle=MKF_K210(com_data);
		HAL_UART_Receive_IT(&huart5,&usart5_rx,1);//重新使能串口接收中断
		
	}
//	if(huart->Instance==USART3){ // ATK-MS901M
//		uint8_t tmp;
//	    if (__HAL_UART_GET_FLAG(&huart3, UART_FLAG_ORE) != RESET)    /* UART 发生过载错误 */
//    {
//        __HAL_UART_CLEAR_OREFLAG(&huart3);                       /* 清除过载错误标志 */
//        (void)huart3.Instance->SR;                               /* 先读SR寄存器 */
//        (void)huart3.Instance->DR;                               /* 再读DR寄存器 */
//    }
//    
//    if (__HAL_UART_GET_FLAG(&huart3, UART_FLAG_RXNE) != RESET)   /* UART 接收到数据 */
//    {
//        HAL_UART_Receive(&huart3, &tmp, 1, HAL_MAX_DELAY);       /* 读取数据 */
//        atk_ms901m_uart_rx_fifo_write(&tmp, 1);                  /* 写入FIFO缓冲 */
//    }
//	}
	}	

		
// K210 数据接收状态机（示例）		
//  if(RxState==0&&K210==0xb3)  //等待帧头1
//				{
//					RxState=1;
//					RxBuffer1[0]=com_data;//0xb3
//				}
//		else if(RxState==1&&K210==0xa3)  //等待帧头2
//				{
//					RxState=2;
//					RxBuffer1[1]=com_data;//0xb3  0xb3
//				}
//	 else if(RxState==2)
//				{  
//					RxBuffer1[2]=com_data;
//					RxState=3;
//					Cy=K210;		
//			}
//		else if(RxState==3)
//		{
//		      RxBuffer1[3]=com_data;
//					RxState=0;
//					Cx=K210;
//			HAL_UART_Transmit(&huart4 , (uint8_t*)Cx, 1, 0xFFFF);
//			for(int i=0;i<4;i++)
//			{
//			RxBuffer1[i]=0x00;      //清空接收缓冲区
//			}
//		}
//		else
//		{
//		RxState=0;
//		RxBuffer1[0]=0x00;      //清空接收缓冲区	
//		}
		
// 另一个K210数据接收逻辑		
//		if((int)K210[0]==0XB3)//判断是否为帧头1
//	{
//		
//		HAL_UART_Receive(&huart3,K210+1,1,0xffff);//接收第二个字节
//		
//		if((int)uart[1]==0XA3)//判断是否为帧头2
//		{
//			HAL_UART_Receive(&huart3,K210+2,2,0xffff);//接收K210的数据
//			Cy=K210[2];
//			Cx=K210[3];
//			//printf("Cx:%d,Cy:%d\r\n",Cx,Cy);
//			for(int i=0;i<4;i++)
//			{
//			K210[i]=0x00;      //清空接收缓冲区
//			}
//			}
//		else
//			K210[0]=0;//否则帧头错误，清零
//	}
//		else
//			K210[0]=0;//否则帧头错误，清零
//		Cx=Cx/10;
		//printf("Cx=%d\r\n",Cx);
//		   uint8_t  cmd[]={0x03,0xfc,Cx,0xfc,0x03};//自定义协议 帧头0X03,0XFC 帧尾0xfC 0x03
//		for(int i=0;i<5;i++)//循环发送
//		HAL_UART_Transmit(&huart1,&cmd[i],1,0xfff);
	 


/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
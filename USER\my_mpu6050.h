/*
 * my_mpu6050.h
 *
 *  Created on: Mar 27, 2021
 *      Author: opsang
 */

#ifndef MPU6050_DMP_MY_MPU6050_H_
#define MPU6050_DMP_MY_MPU6050_H_
#include "main.h"
#include "stm32f4xx_hal.h"
#include "i2c.h"
extern unsigned long sensor_timestamp;
extern unsigned char more;
extern long quat[4];

typedef struct {
	float pitch,roll,yaw,temperature;
	int16_t temp;
	long quat[4];
	short gyro[3], accel[3], sensors;
}Mpu6050_data;
extern Mpu6050_data My_Mpu6050;

static void gyro_data_ready_cb(void);
uint8_t mpu_dmp_init(void);
uint8_t dmp_getdata(void);
void MPU6050_Read_Temp(void);


uint8_t i2c1_write(unsigned char slave_addr, unsigned char reg_addr, unsigned char length, unsigned char *data);

uint8_t i2c1_read(unsigned char slave_addr, unsigned char reg_addr, unsigned char length, unsigned char *data);


#endif /* MPU6050_DMP_MY_MPU6050_H_ */

#include "gpio.h"
#include "main.h"
#include "usart.h"
uint8_t Key_Scam()
{
	if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_4)==0)
	{
		HAL_Delay(20);
		if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_4)==0)
		{
			HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_4);
			return 1;
			printf("D_4\r\n");
		}
	}
	
	if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_12)==0)
	{
		HAL_Delay(20);
		if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_12)==0)
		{
			HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_5);
			printf("D_5\r\n");
			return 2;
		}
	}
	
	if(HAL_GPIO_ReadPin(GPIOD,GPIO_PIN_3)==0)
	{
		HAL_Delay(20);
		if(HAL_GPIO_ReadPin(GPIOD,GPIO_PIN_3)==0)
		{
			HAL_GPIO_TogglePin(GPIOD,GPIO_PIN_6);
			printf("D_6\r\n");
			return 3;
		}
	}
	
	if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_15)==0)
	{
		HAL_Delay(20);
		if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_15)==0)
		{
			HAL_GPIO_TogglePin(GPIOC,GPIO_PIN_13);
			printf("C_13\r\n");
		//	return 4;
		}
	}
	return 0;
}
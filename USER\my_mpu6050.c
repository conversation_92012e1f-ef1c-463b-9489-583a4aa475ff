#include "my_mpu6050.h"

#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "main.h"
#include "stm32f4xx.h"
#include "stm32f4xx_hal.h"
#include "i2c.h"
#include "gpio.h"

#include "inv_mpu.h"
#include "inv_mpu_dmp_motion_driver.h"
#include "CMSIS_ARMCC.h"

//#define self_test

#define MPU6050
/* Data requested by client. */
#define PRINT_ACCEL     (0x01)
#define PRINT_GYRO      (0x02)
#define PRINT_QUAT      (0x04)

#define ACCEL_ON        (0x01)
#define GYRO_ON         (0x02)

#define MOTION          (0)
#define NO_MOTION       (1)

/* Starting sampling rate. */
#define DEFAULT_MPU_HZ  (100)

#define FLASH_SIZE      (512)
#define FLASH_MEM_START ((void*)0x1800)


Mpu6050_data My_Mpu6050;

struct rx_s
{
    unsigned char header[3];
    unsigned char cmd;
};
struct hal_s
{
    unsigned char sensors;
    unsigned char dmp_on;
    unsigned char wait_for_tap;
    volatile unsigned char new_gyro;
    unsigned short report;
    unsigned short dmp_features;
    unsigned char motion_int_mode;
    struct rx_s rx;
};
static struct hal_s hal = {0};

/* USB RX binary semaphore. Actually, it's just a flag. Not included in struct
 * because it's declared extern elsewhere.
 */
volatile unsigned char rx_new;

/* The sensors can be mounted onto the board in any orientation. The mounting
 * matrix seen below tells the MPL how to rotate the raw data from thei
 * driver(s).
 * TODO: The following matrices refer to the configuration on an internal test
 * board at Invensense. If needed, please modify the matrices to match the
 * chip-to-body matrix for your particular set up.
 */
static signed char gyro_orientation[9] = { -1, 0, 0,
        0, -1, 0,
        0, 0, 1
                                         };
//旋转矩阵

//enum packet_type_e {
//    PACKET_TYPE_ACCEL,
//    PACKET_TYPE_GYRO,
//    PACKET_TYPE_QUAT,
//    PACKET_TYPE_TAP,
//    PACKET_TYPE_ANDROID_ORIENT,
//    PACKET_TYPE_PEDO,
//    PACKET_TYPE_MISC
//};

/* Send data to the Python client application.
 * Data is formatted as follows:
 * packet[0]    = $
 * packet[1]    = packet type (see packet_type_e)
 * packet[2+]   = data
 */
//void send_packet(char packet_type, void *data)
//{
//#define MAX_BUF_LENGTH  (18)
//    char buf[MAX_BUF_LENGTH], length;
//
//    memset(buf, 0, MAX_BUF_LENGTH);
//    buf[0] = '$';
//    buf[1] = packet_type;
//
//    if (packet_type == PACKET_TYPE_ACCEL || packet_type == PACKET_TYPE_GYRO) {
//        short *sdata = (short*)data;
//        buf[2] = (char)(sdata[0] >> 8);
//        buf[3] = (char)sdata[0];
//        buf[4] = (char)(sdata[1] >> 8);
//        buf[5] = (char)sdata[1];
//        buf[6] = (char)(sdata[2] >> 8);
//        buf[7] = (char)sdata[2];
//        length = 8;
//    } else if (packet_type == PACKET_TYPE_QUAT) {
//        long *ldata = (long*)data;
//        buf[2] = (char)(ldata[0] >> 24);
//        buf[3] = (char)(ldata[0] >> 16);
//        buf[4] = (char)(ldata[0] >> 8);
//        buf[5] = (char)ldata[0];
//        buf[6] = (char)(ldata[1] >> 24);
//        buf[7] = (char)(ldata[1] >> 16);
//        buf[8] = (char)(ldata[1] >> 8);
//        buf[9] = (char)ldata[1];
//        buf[10] = (char)(ldata[2] >> 24);
//        buf[11] = (char)(ldata[2] >> 16);
//        buf[12] = (char)(ldata[2] >> 8);
//        buf[13] = (char)ldata[2];
//        buf[14] = (char)(ldata[3] >> 24);
//        buf[15] = (char)(ldata[3] >> 16);
//        buf[16] = (char)(ldata[3] >> 8);
//        buf[17] = (char)ldata[3];
//        length = 18;
//    } else if (packet_type == PACKET_TYPE_TAP) {
//        buf[2] = ((char*)data)[0];
//        buf[3] = ((char*)data)[1];
//        length = 4;
//    } else if (packet_type == PACKET_TYPE_ANDROID_ORIENT) {
//        buf[2] = ((char*)data)[0];
//        length = 3;
//    } else if (packet_type == PACKET_TYPE_PEDO) {
//        long *ldata = (long*)data;
//        buf[2] = (char)(ldata[0] >> 24);
//        buf[3] = (char)(ldata[0] >> 16);
//        buf[4] = (char)(ldata[0] >> 8);
//        buf[5] = (char)ldata[0];
//        buf[6] = (char)(ldata[1] >> 24);
//        buf[7] = (char)(ldata[1] >> 16);
//        buf[8] = (char)(ldata[1] >> 8);
//        buf[9] = (char)ldata[1];
//        length = 10;
//    } else if (packet_type == PACKET_TYPE_MISC) {
//        buf[2] = ((char*)data)[0];
//        buf[3] = ((char*)data)[1];
//        buf[4] = ((char*)data)[2];
//        buf[5] = ((char*)data)[3];
//        length = 6;
//    }
//    cdcSendDataWaitTilDone((BYTE*)buf, length, CDC0_INTFNUM, 100);
//}

/* These next two functions converts the orientation matrix (see
 * gyro_orientation) to a scalar representation for use by the DMP.
 * NOTE: These functions are borrowed from Invensense's MPL.
 */
static inline unsigned short inv_row_2_scale(const signed char *row)
{
    unsigned short b;

    if (row[0] > 0)
        b = 0;
    else if (row[0] < 0)
        b = 4;
    else if (row[1] > 0)
        b = 1;
    else if (row[1] < 0)
        b = 5;
    else if (row[2] > 0)
        b = 2;
    else if (row[2] < 0)
        b = 6;
    else
        b = 7;      // error
    return b;
}

static inline unsigned short inv_orientation_matrix_to_scalar(
    const signed char *mtx)
{
    unsigned short scalar;

    /*
       XYZ  010_001_000 Identity Matrix
       XZY  001_010_000
       YXZ  010_000_001
       YZX  000_010_001
       ZXY  001_000_010
       ZYX  000_001_010
     */

    scalar = inv_row_2_scale(mtx);
    scalar |= inv_row_2_scale(mtx + 3) << 3;
    scalar |= inv_row_2_scale(mtx + 6) << 6;

    return scalar;
}

/* Handle sensor on/off combinations. */
//static void setup_gyro(void)
//{
//    unsigned char mask = 0;
//    if (hal.sensors & ACCEL_ON)
//        mask |= INV_XYZ_ACCEL;
//    if (hal.sensors & GYRO_ON)
//        mask |= INV_XYZ_GYRO;
//    /* If you need a power transition, this function should be called with a
//     * mask of the sensors still enabled. The driver turns off any sensors
//     * excluded from this mask.
//     */
//    mpu_set_sensors(mask);
//    if (!hal.dmp_on)
//        mpu_configure_fifo(mask);
//}

//static void tap_cb(unsigned char direction, unsigned char count)
//{
//    char data[2];
//    data[0] = (char)direction;
//    data[1] = (char)count;
//    send_packet(PACKET_TYPE_TAP, data);
//}

//static void android_orient_cb(unsigned char orientation)
//{
//    send_packet(PACKET_TYPE_ANDROID_ORIENT, &orientation);
//}


//static inline void msp430_reset(void)
//{
//    PMMCTL0 |= PMMSWPOR;
//}

void SYSTEM_Reset(void)
{
    __set_FAULTMASK(1); //中断屏蔽
    NVIC_SystemReset();// 复位
}

static inline void run_self_test(void)
{
    int result;
//    char test_packet[4] = {0};
    long gyro[3], accel[3];
    unsigned char i = 0;

#if defined (MPU6500) || defined (MPU9250)
    result = mpu_run_6500_self_test(gyro, accel, 0);
#elif defined (MPU6050) || defined (MPU9150)
    result = mpu_run_self_test(gyro, accel);
#endif
    //只有在某个加速度范围内才能result=7
    if (result == 0x7)
    {
        /* Test passed. We can trust the gyro data here, so let's push it down
         * to the DMP.
         */
        for (i = 0; i < 3; i++)
        {
            gyro[i] = (long)(gyro[i] * 32.8f); //convert to +-1000dps
            accel[i] *= 2048.f; //convert to +-16G
            accel[i] = accel[i] >> 16;
            gyro[i] = (long)(gyro[i] >> 16);
        }

        mpu_set_gyro_bias_reg(gyro);  //校准gyro

#if defined (MPU6500) || defined (MPU9250)
        mpu_set_accel_bias_6500_reg(accel);
#elif defined (MPU6050) || defined (MPU9150)
        mpu_set_accel_bias_6050_reg(accel);  //校准accel
#endif
    }

    /* Report results. */
//    test_packet[0] = 't';
//    test_packet[1] = result;
//    send_packet(PACKET_TYPE_MISC, test_packet);
}


/* Every time new gyro data is available, this function is called in an
 * ISR context. In this example, it sets a flag protecting the FIFO read
 * function.
 */
static void gyro_data_ready_cb(void)
{
    hal.new_gyro = 1;
}

uint8_t mpu_dmp_init(void)
{
    int result;
//    unsigned char accel_fsr;
//    unsigned short gyro_rate, gyro_fsr;
//    unsigned long timestamp;
//    struct int_param_s int_param;


    /* Set up gyro.
     * Every function preceded by mpu_ is a driver function and can be found
     * in inv_mpu.h.
     */
//    int_param.cb = gyro_data_ready_cb;
//    int_param.pin = INT_PIN_P20;
//    int_param.lp_exit = INT_EXIT_LPM0;
//    int_param.active_low = 1;
    result = mpu_init();
    if (result)
    {
        SYSTEM_Reset();
        return 1;
    }


    /* If you're not using an MPU9150 AND you're not using DMP features, this
     * function will place all slaves on the primary bus.
     * mpu_set_bypass(1);
     */

    /* Get/set hardware configuration. Start gyro. */
    /* Wake up all sensors. */
    if (mpu_set_sensors(INV_XYZ_GYRO | INV_XYZ_ACCEL)) //开启传感器
    {
        return 2;
    }
    /* Push both gyro and accel data into the FIFO. */
    if (mpu_configure_fifo(INV_XYZ_GYRO | INV_XYZ_ACCEL)) //配置fifo保存陀螺仪和加速计得原始数据
    {
        return 3;
    }
    if (mpu_set_sample_rate(DEFAULT_MPU_HZ)) //设置100hz频率，不使用dmp的时候才起作用,通过这个频率输出原始数据
    {
        return 4;
    }
    /* Read back configuration in case it was set improperly. */
//    mpu_get_sample_rate(&gyro_rate);
//    mpu_get_gyro_fsr(&gyro_fsr);
//    mpu_get_accel_fsr(&accel_fsr);

    /* Initialize HAL state variables. */
    memset(&hal, 0, sizeof(hal));
    hal.sensors = ACCEL_ON | GYRO_ON;
//    hal.report = PRINT_QUAT;

    /* To initialize the DMP:
     * 1. Call dmp_load_motion_driver_firmware(). This pushes the DMP image in
     *    inv_mpu_dmp_motion_driver.h into the MPU memory.
     * 2. Push the gyro and accel orientation matrix to the DMP.
     * 3. Register gesture callbacks. Don't worry, these callbacks won't be
     *    executed unless the corresponding feature is enabled.
     * 4. Call dmp_enable_feature(mask) to enable different features.
     * 5. Call dmp_set_fifo_rate(freq) to select a DMP output rate.
     * 6. Call any feature-specific control functions.
     *
     * To enable the DMP, just call mpu_set_dmp_state(1). This function can
     * be called repeatedly to enable and disable the DMP at runtime.
     *
     * The following is a short summary of the features supported in the DMP
     * image provided in inv_mpu_dmp_motion_driver.c:
     * DMP_FEATURE_LP_QUAT: Generate a gyro-only quaternion on the DMP at
     * 200Hz. Integrating the gyro data at higher rates reduces numerical
     * errors (compared to integration on the MCU at a lower sampling rate).
     * DMP_FEATURE_6X_LP_QUAT: Generate a gyro/accel quaternion on the DMP at
     * 200Hz. Cannot be used in combination with DMP_FEATURE_LP_QUAT.
     * DMP_FEATURE_TAP: Detect taps along the X, Y, and Z axes.
     * DMP_FEATURE_ANDROID_ORIENT: Google's screen rotation algorithm. Triggers
     * an event at the four orientations where the screen should rotate.
     * DMP_FEATURE_GYRO_CAL: Calibrates the gyro data after eight seconds of
     * no motion.
     * DMP_FEATURE_SEND_RAW_ACCEL: Add raw accelerometer data to the FIFO.
     * DMP_FEATURE_SEND_RAW_GYRO: Add raw gyro data to the FIFO.
     * DMP_FEATURE_SEND_CAL_GYRO: Add calibrated gyro data to the FIFO. Cannot
     * be used in combination with DMP_FEATURE_SEND_RAW_GYRO.
     */
    if (dmp_load_motion_driver_firmware())
    {
        return 5;
    }
    if (dmp_set_orientation(inv_orientation_matrix_to_scalar(gyro_orientation)))
    {
        return 6;
    }
//    dmp_register_tap_cb(tap_cb);
//    dmp_register_android_orient_cb(android_orient_cb);
    /*
     * Known Bug -
     * DMP when enabled will sample sensor data at 200Hz and output to FIFO at the rate
     * specified in the dmp_set_fifo_rate API. The DMP will then sent an interrupt once
     * a sample has been put into the FIFO. Therefore if the dmp_set_fifo_rate is at 25Hz
     * there will be a 25Hz interrupt from the MPU device.
     *
     * There is a known issue in which if you do not enable DMP_FEATURE_TAP
     * then the interrupts will be at 200Hz even if fifo rate
     * is set at a different rate. To avoid this issue include the DMP_FEATURE_TAP
     */
    hal.dmp_features = DMP_FEATURE_6X_LP_QUAT | DMP_FEATURE_TAP |
                       DMP_FEATURE_ANDROID_ORIENT | DMP_FEATURE_SEND_RAW_ACCEL | DMP_FEATURE_SEND_CAL_GYRO |
                       DMP_FEATURE_GYRO_CAL;
    if (dmp_enable_feature(hal.dmp_features))
    {
        return 7;
    }

    if (dmp_set_fifo_rate(DEFAULT_MPU_HZ)) //真正配置dmp输出采样频率
    {
        return 8;
    }
    mpu_set_dmp_state(1);  //打开dmp功能，将mpu采样率和dmp输出采样率设置一致
    hal.dmp_on = 1;  //保存dmp已开启的配置信息

    HAL_Delay(100);
#ifdef self_test
    run_self_test();
#endif
    return 0;
//    __enable_interrupt();
//
//    /* Wait for enumeration. */
//    while (USB_connectionState() != ST_ENUM_ACTIVE);
}

unsigned long sensor_timestamp;
unsigned char more;
#define q30 1073741824.0f
float q0 = 1.0f, q1 = 0.0f, q2 = 0.0f, q3 = 0.0f;

//获取数据,给外部调用的
uint8_t dmp_getdata(void)
{
//     if (hal.motion_int_mode) {
//         /* Enable motion interrupt. */
//          mpu_lp_motion_interrupt(500, 1, 5);
//         hal.new_gyro = 0;
//         /* Wait for the MPU interrupt. */
//         while (!hal.new_gyro)
//             __bis_SR_register(LPM0_bits + GIE);
//         /* Restore the previous sensor configuration. */
//         mpu_lp_motion_interrupt(0, 0, 0);
//         hal.motion_int_mode = 0;
//     }

//     if (!hal.sensors || !hal.new_gyro) {
//         /* Put the MSP430 to sleep until a timer interrupt or data ready
//          * interrupt is detected.
//          */
//         __bis_SR_register(LPM0_bits + GIE);
//         continue;
//     }
    hal.new_gyro = 1;
    if (hal.new_gyro && hal.dmp_on)
    {

        /* This function gets new data from the FIFO when the DMP is in
         * use. The FIFO can contain any combination of gyro, accel,
         * quaternion, and gesture data. The sensors parameter tells the
         * caller which data fields were actually populated with new data.
         * For example, if sensors == (INV_XYZ_GYRO | INV_WXYZ_QUAT), then
         * the FIFO isn't being filled with accel data.
         * The driver parses the gesture data to determine if a gesture
         * event has occurred; on an event, the application will be notified
         * via a callback (assuming that a callback function was properly
         * registered). The more parameter is non-zero if there are
         * leftover packets in the FIFO.
         */
        if (dmp_read_fifo(My_Mpu6050.gyro, My_Mpu6050.accel, My_Mpu6050.quat, &sensor_timestamp, &My_Mpu6050.sensors,
                          &more))
        {
            return 1;
        }

        if (!more)
            hal.new_gyro = 0;
        /* Gyro and accel data are written to the FIFO by the DMP in chip
         * frame and hardware units. This behavior is convenient because it
         * keeps the gyro and accel outputs of dmp_read_fifo and
         * mpu_read_fifo consistent.
         */
//         if (sensors & INV_XYZ_GYRO && hal.report & PRINT_GYRO)
//             send_packet(PACKET_TYPE_GYRO, gyro);
//         if (sensors & INV_XYZ_ACCEL && hal.report & PRINT_ACCEL)
//             send_packet(PACKET_TYPE_ACCEL, accel);
        /* Unlike gyro and accel, quaternions are written to the FIFO in
         * the body frame, q30. The orientation is set by the scalar passed
         * to dmp_set_orientation during initialization.
         */
//         if (sensors & INV_WXYZ_QUAT && hal.report & PRINT_QUAT)
//             send_packet(PACKET_TYPE_QUAT, quat);
    }
//     else if (hal.new_gyro) {
//         short gyro[3], accel[3];
//         unsigned char sensors, more;
//         /* This function gets new data from the FIFO. The FIFO can contain
//          * gyro, accel, both, or neither. The sensors parameter tells the
//          * caller which data fields were actually populated with new data.
//          * For example, if sensors == INV_XYZ_GYRO, then the FIFO isn't
//          * being filled with accel data. The more parameter is non-zero if
//          * there are leftover packets in the FIFO.
//          */
//         mpu_read_fifo(gyro, accel, &sensor_timestamp, &sensors, &more);
//         if (!more)
//             hal.new_gyro = 0;
//         if (sensors & INV_XYZ_GYRO && hal.report & PRINT_GYRO)
//             send_packet(PACKET_TYPE_GYRO, gyro);
//         if (sensors & INV_XYZ_ACCEL && hal.report & PRINT_ACCEL)
//             send_packet(PACKET_TYPE_ACCEL, accel);

    if (My_Mpu6050.sensors & INV_WXYZ_QUAT)
    {
        q0 = My_Mpu6050.quat[0] / q30;
        q1 = My_Mpu6050.quat[1] / q30;
        q2 = My_Mpu6050.quat[2] / q30;
        q3 = My_Mpu6050.quat[3] / q30;
        My_Mpu6050.pitch = asin(-2 * q1 * q3 + 2 * q0 * q2) * 57.3;
        My_Mpu6050.roll = atan2(2 * q2 * q3 + 2 * q0 * q1, -2 * q1 * q1 - 2 * q2 * q2 + 1) * 57.3;
        My_Mpu6050.yaw = atan2(2 * (q1 * q2 + q0 * q3), q0 * q0 + q1 * q1 - q2 * q2 - q3 * q3) * 57.3;
    }
    return 0;
}

uint8_t Rec_Data[2];
int16_t temp;
void MPU6050_Read_Temp(void)
{

    // Read 2 BYTES of data starting from TEMP_OUT_H_REG register
    HAL_I2C_Mem_Read(&hi2c1, 0xd0, 0x41, 1, Rec_Data, 2, 0xffff);
    temp = (int16_t)(Rec_Data[0] << 8 | Rec_Data[1]);
    My_Mpu6050.temperature = (float)((int16_t) temp / (float) 340.0 + (float) 36.53);
}
uint8_t i2c2_write(unsigned char slave_addr, unsigned char reg_addr, unsigned char length, unsigned char *data)
{
	int ret=HAL_I2C_Mem_Write(&hi2c1, slave_addr, reg_addr, I2C_MEMADD_SIZE_8BIT, data, length, 0XFFFF);
	if(ret==HAL_OK)return 0;
	else return -1;
}

uint8_t i2c2_read(unsigned char slave_addr, unsigned char reg_addr, unsigned char length, unsigned char *data)
{
	int ret= HAL_I2C_Mem_Read(&hi2c1, slave_addr, reg_addr, I2C_MEMADD_SIZE_8BIT, data, length, 0XFFFF);
	if(ret==HAL_OK)return 0;
	else return -1;
}